{{-- ################################################################################################################################################################ --}}
{{-- SYARAT FLEKSIBEL F1 --}}

@foreach ($syarat_khas_ff as  $syarat_ff)
    @php
        if($syarat_ff->jum_subjek=='1') { $read='SATU'; }
        elseif($syarat_ff->jum_subjek=='2') { $read='DUA'; }
        elseif($syarat_ff->jum_subjek=='3') { $read='TIGA'; }
        elseif($syarat_ff->jum_subjek=='4') { $read='EMPAT'; }
        elseif($syarat_ff->jum_subjek=='5') { $read='LIMA'; }
        elseif($syarat_ff->jum_subjek=='6') { $read='ENAM'; }
        elseif($syarat_ff->jum_subjek=='7') { $read='TUJUH'; }
        elseif($syarat_ff->jum_subjek=='8') { $read='LAPAN'; }
        elseif($syarat_ff->jum_subjek=='9') { $read='SEMBILAN'; }
    @endphp

    @if($syarat_ff->kumpulan=='F' && $syarat_ff->sub_kumpulan=='F')
        <li style="padding-left: .3em;"> 
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_ff->mingred}}</b> dalam mana-mana <b>{{$read}} ({{$syarat_ff->jum_subjek}})</b> mata pelajaran. 
        </li>
    @endif
@endforeach

@foreach ($syarat_khas_fx as  $syarat_fx)
@if ($loop->first)
    @php
        if($syarat_fx->jum_subjek=='1') { $read='SATU'; }
        elseif($syarat_fx->jum_subjek=='2') { $read='DUA'; }
        elseif($syarat_fx->jum_subjek=='3') { $read='TIGA'; }
        elseif($syarat_fx->jum_subjek=='4') { $read='EMPAT'; }
        elseif($syarat_fx->jum_subjek=='5') { $read='LIMA'; }
        elseif($syarat_fx->jum_subjek=='6') { $read='ENAM'; }
        elseif($syarat_fx->jum_subjek=='7') { $read='TUJUH'; }
        elseif($syarat_fx->jum_subjek=='8') { $read='LAPAN'; }
        elseif($syarat_fx->jum_subjek=='9') { $read='SEMBILAN'; }
    @endphp

    @if($syarat_fx->kumpulan=='F' && $syarat_fx->sub_kumpulan=='X')
        <li style="padding-left: .3em;"> 
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_fx->mingred}}</b> dalam mana-mana <b>{{$read}} ({{$syarat_fx->jum_subjek}})</b> mata pelajaran yang belum diambil kira.
        </li>
    @endif
@endif
@endforeach

@foreach ($syarat_khas_fy as  $syarat_fy)
    @php
        if($syarat_fy->jum_subjek=='1') { $read='SATU'; }
        elseif($syarat_fy->jum_subjek=='2') { $read='DUA'; }
        elseif($syarat_fy->jum_subjek=='3') { $read='TIGA'; }
        elseif($syarat_fy->jum_subjek=='4') { $read='EMPAT'; }
        elseif($syarat_fy->jum_subjek=='5') { $read='LIMA'; }
        elseif($syarat_fy->jum_subjek=='6') { $read='ENAM'; }
        elseif($syarat_fy->jum_subjek=='7') { $read='TUJUH'; }
        elseif($syarat_fy->jum_subjek=='8') { $read='LAPAN'; }
        elseif($syarat_fy->jum_subjek=='9') { $read='SEMBILAN'; }
    @endphp

    @if($syarat_fy->kumpulan=='F' && $syarat_fy->sub_kumpulan=='Y')
        <li style="padding-left: .3em;"> 
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_fy->mingred}}</b> dalam mana-mana <b>{{$read}} ({{$syarat_fy->jum_subjek}})</b> mata pelajaran selain diatas
        </li>
    @endif
@endforeach

{{-- ################################################################################################################################################################ --}}
{{-- SYARAT FLEKSIBEL F2 --}}
@foreach ($syarat_khas_f2f as  $syarat_f2f)
    @php
        if($syarat_f2f->jum_subjek=='1') { $read='SATU'; }
        elseif($syarat_f2f->jum_subjek=='2') { $read='DUA'; }
        elseif($syarat_f2f->jum_subjek=='3') { $read='TIGA'; }
        elseif($syarat_f2f->jum_subjek=='4') { $read='EMPAT'; }
        elseif($syarat_f2f->jum_subjek=='5') { $read='LIMA'; }
        elseif($syarat_f2f->jum_subjek=='6') { $read='ENAM'; }
        elseif($syarat_f2f->jum_subjek=='7') { $read='TUJUH'; }
        elseif($syarat_f2f->jum_subjek=='8') { $read='LAPAN'; }
        elseif($syarat_f2f->jum_subjek=='9') { $read='SEMBILAN'; }
    @endphp

    @if($syarat_f2f->kumpulan=='F2' && $syarat_f2f->sub_kumpulan=='F')
        <li style="padding-left: .3em;"> 
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_f2f->mingred}}</b> dalam mana-mana <b>{{$read}} ({{$syarat_f2f->jum_subjek}})</b> mata pelajaran. 
        </li>
    @endif
@endforeach

@foreach ($syarat_khas_f2x as  $syarat_f2x)
    @php
        if($syarat_f2x->jum_subjek=='1') { $read='SATU'; }
        elseif($syarat_f2x->jum_subjek=='2') { $read='DUA'; }
        elseif($syarat_f2x->jum_subjek=='3') { $read='TIGA'; }
        elseif($syarat_f2x->jum_subjek=='4') { $read='EMPAT'; }
        elseif($syarat_f2x->jum_subjek=='5') { $read='LIMA'; }
        elseif($syarat_f2x->jum_subjek=='6') { $read='ENAM'; }
        elseif($syarat_f2x->jum_subjek=='7') { $read='TUJUH'; }
        elseif($syarat_f2x->jum_subjek=='8') { $read='LAPAN'; }
        elseif($syarat_f2x->jum_subjek=='9') { $read='SEMBILAN'; }
    @endphp

    @if($syarat_f2x->kumpulan=='F2' && $syarat_f2x->sub_kumpulan=='X')
        <li style="padding-left: .3em;"> 
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_f2x->mingred}}</b> dalam mana-mana <b>{{$read}} ({{$syarat_f2x->jum_subjek}})</b> mata pelajaran yang belum diambil kira.
        </li>
    @endif
@endforeach

@foreach ($syarat_khas_f2y as  $syarat_f2y)
    @php
        if($syarat_f2y->jum_subjek=='1') { $read='SATU'; }
        elseif($syarat_f2y->jum_subjek=='2') { $read='DUA'; }
        elseif($syarat_f2y->jum_subjek=='3') { $read='TIGA'; }
        elseif($syarat_f2y->jum_subjek=='4') { $read='EMPAT'; }
        elseif($syarat_f2y->jum_subjek=='5') { $read='LIMA'; }
        elseif($syarat_f2y->jum_subjek=='6') { $read='ENAM'; }
        elseif($syarat_f2y->jum_subjek=='7') { $read='TUJUH'; }
        elseif($syarat_f2y->jum_subjek=='8') { $read='LAPAN'; }
        elseif($syarat_f2y->jum_subjek=='9') { $read='SEMBILAN'; }
    @endphp

    @if($syarat_f2y->kumpulan=='F2' && $syarat_f2y->sub_kumpulan=='Y')
        <li style="padding-left: .3em;"> 
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_f2y->mingred}}</b> dalam mana-mana <b>{{$read}} ({{$syarat_f2y->jum_subjek}})</b> mata pelajaran selain diatas
        </li>
    @endif
@endforeach

{{-- ################################################################################################################################################################ --}}
{{-- SYARAT FLEKSIBEL F3 --}}

@foreach ($syarat_khas_f3 as  $syarat_f3)


    @if($syarat_f3->kumpulan=='F3' && $syarat_f3->sub_kumpulan=='F3')
        @php
            $syarat_xkesetaraan = DB::connection('kpt')->table('upuplus_xsub_kumpulan_subjek')->where('kodprogram',$syarat_f3->kodprogram)->where('kategori',$syarat_f3->kategori)->orderby('orderid','ASC')->get();

            if($syarat_f3->jum_subjek=='1') { $read='SATU'; }
            elseif($syarat_f3->jum_subjek=='2') { $read='DUA'; }
            elseif($syarat_f3->jum_subjek=='3') { $read='TIGA'; }
            elseif($syarat_f3->jum_subjek=='4') { $read='EMPAT'; }
            elseif($syarat_f3->jum_subjek=='5') { $read='LIMA'; }
            elseif($syarat_f3->jum_subjek=='6') { $read='ENAM'; }
            elseif($syarat_f3->jum_subjek=='7') { $read='TUJUH'; }
            elseif($syarat_f3->jum_subjek=='8') { $read='LAPAN'; }
            elseif($syarat_f3->jum_subjek=='9') { $read='SEMBILAN'; }

        @endphp

        <li style="padding-left: .3em;">
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_f3->mingred}}</b> dalam <b>{{$read}} ({{$syarat_f3->jum_subjek}})</b> mata pelajaran tidak termasuk mata pelajaran berikut :      
            
            <div class="card bg-light text-dark">
                <div class="card-body p-2">
                    @foreach ($syarat_xkesetaraan as $bil_no => $kumpulan_x_subjek)
                        @if($syarat_f3->kodprogram==$kumpulan_x_subjek->kodprogram)                                                                    
                            @foreach($sksubjek as $sk_subjek)
                                @if($kumpulan_x_subjek->kodsubjek==$sk_subjek->kodsubjekspm)

                                <ul style="list-style-type:disc" class="ml-n3">
                                    <li>{{ ucwords(strtolower($sk_subjek->ketsubjekspm)) }}</li>
                                </ul>
                                {{-- {{ ucwords(strtolower($sk_subjek->ketsubjekspm)) }} @if($bil_no+1 != count($syarat_xkesetaraan)) / @endif  --}}
                                @endif
                            @endforeach                                                                      
                        @endif
                    @endforeach
                </div>
            </div>
        </li>
    @endif

@endforeach



