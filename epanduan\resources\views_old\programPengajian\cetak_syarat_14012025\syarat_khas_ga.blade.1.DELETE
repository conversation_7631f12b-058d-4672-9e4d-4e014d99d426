@if(count($syaratkhas_g1_sk) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g1_sk[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g1_sk[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g1_sk[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_g1_sk as $syarat_khas_g1_sk)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_g1_sk->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
</li>
@endif

@if(count($syaratkhas_g2_sk) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g2_sk[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g2_sk[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g2_sk[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_g2_sk as $syarat_khas_g2_sk)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_g2_sk->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
</li>
@endif

@if(count($syaratkhas_g3_sk) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g3_sk[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g3_sk[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g3_sk[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_g3_sk as $syarat_khas_g3_sk)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_g3_sk->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
</li>
@endif



@if(count($syaratkhas_g1_sk1) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g1_sk1[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g1_sk1[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g1_sk1[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_g1_sk1 as $syarat_khas_g1_sk1)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_g1_sk1->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
</li>
@endif

@if(count($syaratkhas_g2_sk1) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g2_sk1[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g2_sk1[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g2_sk1[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_g2_sk1 as $syarat_khas_g2_sk1)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_g2_sk1->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
</li>
@endif

@if(count($syaratkhas_g3_sk1) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g3_sk1[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g3_sk1[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g3_sk1[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_g3_sk1 as $syarat_khas_g3_sk1)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_g3_sk1->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
</li>
@endif


@if(count($syaratkhas_g1_sk2) > 0 || count($syaratkhas_g2_sk2) > 0)
<br>
<p><b>ATAU</b></p>
@endif


@if(count($syaratkhas_g1_sk2) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g1_sk2[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g1_sk2[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g1_sk2[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_g1_sk2 as $syarat_khas_g1_sk2)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_g1_sk2->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
@endif

@if(count($syaratkhas_g2_sk2) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g2_sk2[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g2_sk2[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g2_sk2[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_g2_sk2 as $syarat_khas_g2_sk2)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_g2_sk2->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
@endif

@if(count($syaratkhas_g3_sk2) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g3_sk2[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g3_sk2[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g3_sk2[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_g3_sk2 as $syarat_khas_g3_sk2)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_g3_sk2->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
</li>
@endif


@if(count($syaratkhas_g1_sk3) > 0 || count($syaratkhas_g2_sk3) > 0)
<br>
<p><b>ATAU</b></p>
@endif

@if(count($syaratkhas_g1_sk3) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g1_sk3[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g1_sk3[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g1_sk3[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_g1_sk3 as $syarat_khas_g1_sk3)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_g1_sk3->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
@endif

@if(count($syaratkhas_g2_sk3) > 0)
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g2_sk3[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g2_sk3[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g2_sk3[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_g2_sk3 as $syarat_khas_g2_sk3)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_g2_sk3->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
@endif

@if(count($syaratkhas_g3_sk3) > 0)
<li style="padding-left: .3em;"> 
    Mendapat sekurang-kurangnya Gred <b>{{$syaratkhas_g3_sk3[0]->MINGRED}}</b> dalam <b>{{$syaratkhas_g3_sk3[0]->KET_JUMLAH_MIN_SUBJEK}} ({{$syaratkhas_g3_sk3[0]->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran berikut :
    <div class="card bg-light text-dark">
        <div class="card-body p-2">
            @foreach ($syaratkhas_g3_sk3 as $syarat_khas_g3_sk3)
                    <span style="margin-right: 6px;font-size: 12px; padding-left: 7px;">&#9679;</span>
                    {{ ucwords(strtolower($syarat_khas_g3_sk3->KODSUBJEK_2)) }} <br>
                @endforeach
        </div>
    </div>
</li>

<br>
@endif



