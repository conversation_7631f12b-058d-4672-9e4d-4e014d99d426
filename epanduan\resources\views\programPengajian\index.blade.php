@extends('layout.layout')

@section('content')
    <section class="bg-half bg-light d-table w-100">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-12 text-center">
                    <div class="page-next-level">
                        <h3 class="title"> PROGRAM PENGAJIAN</h3>
                        <div class="page-next">
                            <nav aria-label="breadcrumb" class="d-inline-block">
                                <ul class="breadcrumb bg-white rounded shadow mb-0">
                                    <li class="breadcrumb-item"><a href="{{ url('/') }}"><PERSON><PERSON></a></li>
                                    <li class="breadcrumb-item"><a
                                            href="{{ url('kategoriCalon', [session('jenprog')]) }}">Kategori
                                            {{ Request::route('kodkatag') }}</a></li>
                                    <li class="breadcrumb-item"><a href="#">Program Pengajian</a></li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="position-relative">
        <div class="shape overflow-hidden text-white">
            <svg viewBox="0 0 2880 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 48H1437.5H2880V0H2160C1442.5 52 720 0 720 0H0V48Z" fill="currentColor"></path>
            </svg>
        </div>
    </div>

    <section class="section">
        <div class="container">
            <form action="{{ url('ProgramPengajian/kategoriCalon/' . Request::route('kodkatag')) }}" method="post">
                @csrf
                <div class="row">
                    <div class="col-lg-3 col-md-4 col-12">
                        <div class="card border-0 sidebar sticky-bar">
                            <div class="card-body p-0">
                                <!-- SEARCH -->
                                <div class="widget">
                                    <div id="search2" class="widget-search mb-0">
                                        <form class="searchform">
                                            <div>
                                                <input type="text" class="border rounded" id="fuzzySearch"
                                                    name="fuzzySearch" placeholder="Carian Program..."
                                                    value="{{ old('fuzzySearch') }}">
                                            </div>
                                        </form>
                                    </div>
                                </div>

                                <div class="widget mt-4 pt-2">
                                    <div class="row" style="margin-bottom: -1.3rem">
                                        <div class="column"
                                            style="float: left;
                                    width: 50%;
                                    margin-top: 6px; margin-left: 16px; margin-right: -25px;">
                                            <h5 class="widget-title">Tapisan</h5>
                                        </div>
                                        <div class="uncheck-clear-button" style="text-align: right">
                                            <button type="submit" class="badge badge-pill badge-success" id="searching" name="searching" style="border: 1px solid #000; padding-left: 12px; padding-right: 12px;;"><i class="fas fa-search"></i> Cari</button>
                                            <button type="submit" class="badge badge-pill badge-danger" id="clearFiltering" name="clearFiltering" style="border: 1px solid #000; padding-left: 9px; padding-right: 9px;"><i class="fas fa-trash-alt"></i> Clear</button>
                                        </div>
                                    </div>

                                    <ul class="list-unstyled mt-4 mb-0 blog-categories sidebar-menu">
                                        <li class="have-children"><a href="#"><span class="fa fa-th-list"
                                                    style="padding-right: 7px;"></span>Bidang</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" name="pBidang[]" value="00"
                                                                    @if (!empty(session()->get('jBIDANG')) && in_array('00', session()->get('jBIDANG'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck1">
                                                                <label class="custom-control-label"
                                                                    for="customCheck1">Program Dan Kelayakan Generik</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="01" name="pBidang[]"
                                                                    @if (!empty(session()->get('jBIDANG')) && in_array('01', session()->get('jBIDANG'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck2">
                                                                <label class="custom-control-label"
                                                                    for="customCheck2">Pendidikan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="02" name="pBidang[]"
                                                                    @if (!empty(session()->get('jBIDANG')) && in_array('02', session()->get('jBIDANG'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck4">
                                                                <label class="custom-control-label"
                                                                    for="customCheck4">Sastera
                                                                    Dan Kemanusiaan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="03" name="pBidang[]"
                                                                    @if (!empty(session()->get('jBIDANG')) && in_array('03', session()->get('jBIDANG'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck5">
                                                                <label class="custom-control-label"
                                                                    for="customCheck5">Sains
                                                                    Sosial, Kewartawanan Dan Maklumat</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="04" name="pBidang[]"
                                                                    @if (!empty(session()->get('jBIDANG')) && in_array('04', session()->get('jBIDANG'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck6">
                                                                <label class="custom-control-label"
                                                                    for="customCheck6">Perniagaan, Pentadbiran Dan
                                                                    Perundangan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="05" name="pBidang[]"
                                                                    @if (!empty(session()->get('jBIDANG')) && in_array('05', session()->get('jBIDANG'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck7">
                                                                <label class="custom-control-label"
                                                                    for="customCheck7">Sains Semulajadi, Matematik Dan
                                                                    Statistik</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="06" name="pBidang[]"
                                                                    @if (!empty(session()->get('jBIDANG')) && in_array('06', session()->get('jBIDANG'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck8">
                                                                <label class="custom-control-label"
                                                                    for="customCheck8">Teknologi Maklumat Dan
                                                                    Komunikasi</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="07" name="pBidang[]"
                                                                    @if (!empty(session()->get('jBIDANG')) && in_array('07', session()->get('jBIDANG'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck9">
                                                                <label class="custom-control-label"
                                                                    for="customCheck9">Kejuruteraan, Pembuatan Dan
                                                                    Pembinaan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="08" name="pBidang[]"
                                                                    @if (!empty(session()->get('jBIDANG')) && in_array('08', session()->get('jBIDANG'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck10">
                                                                <label class="custom-control-label"
                                                                    for="customCheck10">Pertanian, Perhutanan, Perikanan
                                                                    Dan Vaterinar</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="09" name="pBidang[]"
                                                                    @if (!empty(session()->get('jBIDANG')) && in_array('09', session()->get('jBIDANG'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck11">
                                                                <label class="custom-control-label"
                                                                    for="customCheck11">Kesihatan Dan kebajikan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="10" name="pBidang[]"
                                                                    @if (!empty(session()->get('jBIDANG')) && in_array('10', session()->get('jBIDANG'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck12">
                                                                <label class="custom-control-label"
                                                                    for="customCheck12">Perkhidmatan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <li class="have-children"><a href="#"><span class="fa fa-university"
                                                    style="padding-right: 7px;"></span>IPTA</a>
                                            <ul>
                                                @if (session()->get('jenprog') == 'spm')
                                                    {{-- <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="IA"
                                                                        name="carianIPTA[]"
                                                                        @if (!empty(session()->get('jIPTA')) && in_array('IA', session()->get('jIPTA'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA3">
                                                                    <label class="custom-control-label"
                                                                        for="carianIPTA3">ADTEC</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li> --}}
                                                    {{-- <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="NT"
                                                                        name="carianIPTA[]"
                                                                        @if (!empty(session()->get('jIPTA')) && in_array('NT', session()->get('jIPTA'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA6">
                                                                    <label class="custom-control-label"
                                                                        for="carianIPTA6">BPLKP</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li> --}}
                                                    {{-- <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="OI"
                                                                        name="carianIPTA[]"
                                                                        @if (!empty(session()->get('jIPTA')) && in_array('OI', session()->get('jIPTA'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA7">
                                                                    <label class="custom-control-label"
                                                                        for="carianIPTA7">IKM</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li> --}}
                                                    {{-- <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="XT"
                                                                        name="carianIPTA[]"
                                                                        @if (!empty(session()->get('jIPTA')) && in_array('XT', session()->get('jIPTA'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA29">
                                                                    <label class="custom-control-label"
                                                                        for="carianIPTA29">ILKBS</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li> --}}
                                                    {{-- <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="IK"
                                                                        name="carianIPTA[]"
                                                                        @if (!empty(session()->get('jIPTA')) && in_array('IK', session()->get('jIPTA'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA5">
                                                                    <label class="custom-control-label"
                                                                        for="carianIPTA5">ILP</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li> --}}
                                                    {{-- <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="IJ"
                                                                        name="carianIPTA[]"
                                                                        @if (!empty(session()->get('jIPTA')) && in_array('IJ', session()->get('jIPTA'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA4">
                                                                    <label class="custom-control-label"
                                                                        for="carianIPTA4">JMTI</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li> --}}
                                                    {{-- <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="OK"
                                                                        name="carianIPTA[]"
                                                                        @if (!empty(session()->get('jIPTA')) && in_array('OK', session()->get('jIPTA'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA8">
                                                                    <label class="custom-control-label"
                                                                        for="carianIPTA8">KKTM</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li> --}}
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="OM"
                                                                        name="carianIPTA[]"
                                                                        @if (!empty(session()->get('jIPTA')) && in_array('OM', session()->get('jIPTA'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA30">
                                                                    <label class="custom-control-label" for="carianIPTA30">Kolej Mara</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="FC"
                                                                        name="carianIPTA[]"
                                                                        @if (!empty(session()->get('jIPTA')) && in_array('FC', session()->get('jIPTA'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA2">
                                                                    <label class="custom-control-label" for="carianIPTA2">Kolej Komuniti</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="OP"
                                                                        name="carianIPTA[]"
                                                                        @if (!empty(session()->get('jIPTA')) && in_array('OP', session()->get('jIPTA'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA31">
                                                                    <label class="custom-control-label" for="carianIPTA31">KPM</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="FB"
                                                                        name="carianIPTA[]"
                                                                        @if (!empty(session()->get('jIPTA')) && in_array('FB', session()->get('jIPTA'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA1">
                                                                    <label class="custom-control-label"
                                                                        for="carianIPTA1">Politeknik</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                @endif
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UY" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UY', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA27">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA27">UIAM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UE" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UE', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA13">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA13">UITM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UK" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UK', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA17">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA17">UKM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UM" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UM', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA19">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA19">UM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UJ" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UJ', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA16">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA16">UMPSA</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UL" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UL', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA18">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA18">UMK</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UH" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UH', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA15">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA15">UMS</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UG" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UG', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA14">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA14">UMT</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UR" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UR', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA22">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA22">UNIMAP</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UW" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UW', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA26">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA26">UNIMAS</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UD" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UD', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA12">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA12">UNISZA</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UP" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UP', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA20">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA20">UPM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UZ" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UZ', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA28">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA28">UPNM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UA" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UA', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA9">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA9">UPSI</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UQ" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UQ', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA21">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA21">USIM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="US" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('US', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA23">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA23">USM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UT" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UT', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA24">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA24">UTM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UC" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UC', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA11">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA11">UTeM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UB" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UB', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA10">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA10">UTHM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UU" name="carianIPTA[]"
                                                                    @if (!empty(session()->get('jIPTA')) && in_array('UU', session()->get('jIPTA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA25">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA25">UUM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        @if (session()->get('jenprog') == 'spm')
                                            <li class="have-children"><a href="#"><span class="fa fa-code-fork"
                                                        style="padding-right: 7px;"></span>Peringkat Pengajian</a>
                                                <ul>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" name="peringkatPengajian[]"
                                                                        value="0"
                                                                        @if (!empty(session()->get('jPERINGKATpengajian')) && in_array('0', session()->get('jPERINGKATpengajian'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck37">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck37">Asasi/Matrikulasi</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="1"
                                                                        name="peringkatPengajian[]"
                                                                        @if (!empty(session()->get('jPERINGKATpengajian')) && in_array('1', session()->get('jPERINGKATpengajian'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck38">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck38">Sijil-Kredit Graduan Min
                                                                        15</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="2"
                                                                        name="peringkatPengajian[]"
                                                                        @if (!empty(session()->get('jPERINGKATpengajian')) && in_array('2', session()->get('jPERINGKATpengajian'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck39">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck39">Sijil-Kredit Graduan Min
                                                                        30</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="3"
                                                                        name="peringkatPengajian[]"
                                                                        @if (!empty(session()->get('jPERINGKATpengajian')) && in_array('3', session()->get('jPERINGKATpengajian'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck40">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck40">Sijil-Kredit Graduan Min
                                                                        60</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="4"
                                                                        name="peringkatPengajian[]"
                                                                        @if (!empty(session()->get('jPERINGKATpengajian')) && in_array('4', session()->get('jPERINGKATpengajian'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck41">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck41">Diploma</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="5"
                                                                        name="peringkatPengajian[]"
                                                                        @if (!empty(session()->get('jPERINGKATpengajian')) && in_array('5', session()->get('jPERINGKATpengajian'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck42">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck42">Diploma Lanjutan</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="6"
                                                                        name="peringkatPengajian[]"
                                                                        @if (!empty(session()->get('jPERINGKATpengajian')) && in_array('6', session()->get('jPERINGKATpengajian'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck43">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck43">Sarjana Muda</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </li>
                                        @endif
                                        {{-- @if (session()->get('jenprog') == 'stpm')
                                            <li class="have-children"><a href="#"><span class="fa fa-bookmark"
                                                        style="padding-right: 7px;"></span> Minimum PNGK</a>
                                                <ul>

                                                </ul>
                                            </li>
                                            <li class="have-children"><a href="#"><span class="fa fa-tags"
                                                        style="padding-right: 7px;"></span>Minimum MUET</a>
                                                <ul>

                                                </ul>
                                            </li>
                                        @endif --}}
                                        <li class="have-children"><a href="#"><span class="fa fa-cogs"
                                                    style="padding-right: 7px;"></span>Program TVET</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="Y" name="pTVET[]"
                                                                    @if (!empty(session()->get('jTVET')) && in_array('Y', session()->get('jTVET'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck33">
                                                                <label class="custom-control-label"
                                                                    for="customCheck33">Ya</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="T" name="pTVET[]"
                                                                    @if (!empty(session()->get('jTVET')) && in_array('T', session()->get('jTVET'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck34">
                                                                <label class="custom-control-label"
                                                                    for="customCheck34">Tidak</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <li class="have-children"><a href="#"><span class="fa fa-pie-chart"
                                                    style="padding-right: 7px;"></span>Mod Pengajian</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="Y"
                                                                    name="ModPengajian[]"
                                                                    @if (!empty(session()->get('jMODpengajian')) && in_array('Y', session()->get('jMODpengajian'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck35">
                                                                <label class="custom-control-label"
                                                                    for="customCheck35">2U2I/3U1I</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="T"
                                                                    name="ModPengajian[]"
                                                                    @if (!empty(session()->get('jMODpengajian')) && in_array('T', session()->get('jMODpengajian'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck36">
                                                                <label class="custom-control-label"
                                                                    for="customCheck36">Konvensional</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        @if (session()->get('jenprog') == 'stpm')
                                            <li class="have-children"><a href="#"><span class="fa fa-certificate"
                                                        style="padding-right: 7px;"></span>Joint/Dual/Double Degree</a>
                                                <ul>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="Y"
                                                                        name="pDoubleDegree[]"
                                                                        @if (!empty(session()->get('jDOUBLE_DEGREE')) && in_array('Y', session()->get('jDOUBLE_DEGREE'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck44">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck44">Ya</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="T"
                                                                        name="pDoubleDegree[]"
                                                                        @if (!empty(session()->get('jDOUBLE_DEGREE')) && in_array('T', session()->get('jDOUBLE_DEGREE'))) checked @endif
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck45">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck45">Tidak</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </li>
                                        @endif
                                        <li class="have-children"><a href="#"><span class="fa fa-hashtag"
                                                    style="padding-right: 7px;"></span>Bertemu duga / Ujian</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="Y" name="pTemuduga[]"
                                                                    @if (!empty(session()->get('jTEMUDUGA')) && in_array('Y', session()->get('jTEMUDUGA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck31">
                                                                <label class="custom-control-label"
                                                                    for="customCheck31">Ya</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="T" name="pTemuduga[]"
                                                                    @if (!empty(session()->get('jTEMUDUGA')) && in_array('T', session()->get('jTEMUDUGA'))) checked @endif
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck32">
                                                                <label class="custom-control-label"
                                                                    for="customCheck32">Tidak</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>

										@if(substr(Request::route('kodkatag'),0,1)!='G' && substr(Request::route('kodkatag'),0,1)!='E' && substr(Request::route('kodkatag'),0,1)!='F')
                                        <li class="have-children"><a href="#"><span class="fa fa-line-chart"
                                                    style="padding-right: 7px;"></span>Minimum Purata Merit</a>
                                            <ul>
                                                <li>
                                                    <input type="range" min="0" max="100" value="0.00"
                                                        step="0.01" style="width: 60%" id="meritProgram"
                                                        name="meritProgram">
                                                    <b><label id="rangeDisplay" style="padding-left: 1rem"></label>%</b>
                                                </li>
                                            </ul>
                                        </li>
										@endif
                                        {{-- <li>
                                            <button type="submit" class="btn btn-primary" id="searching"
                                                name="searching"
                                                style="width:100%; margin-top: 2rem; max-width: -webkit-fill-available;">Cari</button>
                                        </li> --}}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-9 col-md-8 col-12 mt-5 pt-2 mt-sm-0 pt-sm-0">
                        <div class="row align-items-center">
                            <div class="col-lg-8 col-md-7">
                                <div class="section-title">
                                    <h5 class="mb-0">Paparan
                                        {{ $SENARAI_PROGRAM->firstItem() }} - {{ $SENARAI_PROGRAM->lastItem() }} daripada
                                        {{ $SENARAI_PROGRAM->total() }} carian</h5>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            @if (count($SENARAI_PROGRAM) != '0')
                                @foreach ($SENARAI_PROGRAM as $PROGRAM)
                                    <div class="col-12 mt-4 pt-2">
                                        <div class="card shop-list border-0 shadow position-relative">
                                            <div class="row align-items-center no-gutters">
                                                <div class="col-lg-4 col-md-6">
                                                    @include('programPengajian.logoUA-ILKA')
                                                </div>

                                                <div class="col-lg-8 col-md-6">
                                                    <div class="card-body content p-4">
                                                        <a class="text-dark product-name h5"><b>{{ $PROGRAM->nama_Program }}
                                                                @if ($PROGRAM->program_Temuduga == 'Y')
                                                                    <b>#</b>
                                                                @endif

                                                            </b></a>
                                                        <div class="d-lg-flex align-items-center mt-2 mb-3">
                                                            <h6 class="text-muted small mb-0 mr-3"
                                                                style="margin-top: -0.8rem">
                                                                {{ Str::title($PROGRAM->nama_IPTA) }}
                                                            </h6>
                                                        </div>

														<div class="mt-n3 mb-n2">
														@if (session()->get('jenprog') == 'spm')
															@if ($PROGRAM->program_FEEDER == 'Y')
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-warning">
																<b>Program Perintis (Feeder)</b>
																	<i class="fas fa-info-circle text-light"
																	data-toggle="tooltip"
																	data-placement="right"
																	data-html="true"
																	title="Program pembekal kepada program Ijazah Sarjana Muda di Universiti Awam berkenaan sahaja."></i>
																</span>
															@endif
															@if ($PROGRAM->program_STEM == 'Y')
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-warning"><b>STEM</b></span>
															@endif
															@if ($PROGRAM->program_TVET == 'Y')
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-warning">
																<b>TVET</b>
																	<i class="fas fa-info-circle text-light"
																	data-toggle="tooltip"
																	data-placement="right"
																	data-html="true"
																	title="Program pengajian yang berfokus kepada pembangunan bakat holistik iaitu memiliki pengetahuan teknikal, kemahiran dan insaniah."></i>
																</span>
															@endif
														@endif

														@if (session()->get('jenprog') == 'stpm')
															@if ($PROGRAM->program_KOMPETITIF == 'Y')
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-danger"><b>Kompetitif</b></span>
															@endif
															@if ($PROGRAM->program_BTECH == 'Y')
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-warning"><b>BTECH</b></span>
															@endif
															@if ($PROGRAM->program_TVET == 'Y')
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-warning">
																	<b>TVET</b>
																	<i class="fas fa-info-circle text-light"
																	data-toggle="tooltip"
																	data-placement="right"
																	data-html="true"
																	title="Program pengajian yang berfokus kepada pembangunan bakat holistik iaitu memiliki pengetahuan teknikal, kemahiran dan insaniah."></i>
																</span>
															@endif
														@endif
														</div>

                                                        {{--  <p class="para-desc text-muted mb-1" style="margin-top: 1.3rem;">
                                                            Kod Program :
                                                        </p>  --}}
                                                        {{-- <p class="para-desc text-muted mb-1"><i class="fa fa-tag"> </i><b>
                                                                {{ $PROGRAM->kod_Program }} </b></p> --}}

                                                        <div class="form-row mt-4">
                                                            <div class="form-group col-md-4 mb-0">
                                                                <h6 class="border-bottom badge-line text-center">
                                                                    <label class="mb-n2 badge badge-deeppink rounded-sm col-md-12"><i class="fas fa-tag"></i> Kod Program</label>
                                                                    <small><b>{{ $PROGRAM->kod_Program }}</b></small>
                                                                </h6>
                                                            </div>

                                                            <div class="form-group col-md-4 mb-0">
                                                                <h6 class="border-bottom badge-line text-center">
                                                                    <label class="mb-n2 badge badge-deeppink rounded-sm col-md-12"><i class="far fa-calendar-alt"></i> Tahun</label>
                                                                    <small><b>{{ session()->get('tahun_semasa') }}</b></small>
                                                                </h6>
                                                            </div>

                                                            {{-- <div class="form-group col-md-1"></div> --}}

                                                            @if(substr(Request::route('kodkatag'),0,1)!='G' && substr(Request::route('kodkatag'),0,1)!='E' && substr(Request::route('kodkatag'),0,1)!='F')
                                                            <div class="form-group col-md-4">
                                                                <h6 class="border-bottom badge-line text-center">
                                                                    <label class="mb-n2 badge badge-deeppink rounded-sm col-md-12">
                                                                        <i class="fas fa-calculator"></i> Purata Markah Merit</label>
                                                                    <small>
                                                                        @foreach ($MAKLUMAT_PENGAJIAN as $maklumat_Pengajian)
                                                                            @if ($maklumat_Pengajian->kod_Program == $PROGRAM->kod_Program)
                                                                                @if($maklumat_Pengajian->kategori_Pengajian == 'T')
                                                                                    <b>@if($maklumat_Pengajian->merit_Program=='') Tiada @else {!! $maklumat_Pengajian->merit_Program !!}%
                                                                                        <i class="fas fa-info-circle text-info"
                                                                                        data-toggle="tooltip"
                                                                                        data-placement="right"
                                                                                        data-html="true"
                                                                                        title="<b><u>Penafian</u></b> <br> Tiada jaminan mendapat tawaran berdasarkan markah merit semata-mata. Kejayaan mendapat tawaran bergantung kepada kedudukan merit, memenuhi syarat am dan syarat khas, bilangan tempat yang disediakan dan lulus temu duga dan/atau ujian bagi program pengajian yang menetapkan keperluan berkenaan."></i>
                                                                                        @endif
                                                                                    </b>
                                                                                @endif

                                                                                @if($maklumat_Pengajian->kategori_Pengajian != 'T')
                                                                                    <b>
                                                                                        @if($maklumat_Pengajian->merit_Program=='') Tiada @else {!! $maklumat_Pengajian->merit_Program !!}%
                                                                                        <i class="fas fa-info-circle text-info"
                                                                                        data-toggle="tooltip"
                                                                                        data-placement="right"
                                                                                        data-html="true"
                                                                                        title="<b><u>Penafian</u></b> <br> Tiada jaminan mendapat tawaran berdasarkan markah merit semata-mata. Kejayaan mendapat tawaran bergantung kepada kedudukan merit, memenuhi syarat am dan syarat khas, bilangan tempat yang disediakan dan lulus temu duga dan/atau ujian bagi program pengajian yang menetapkan keperluan berkenaan."></i>
                                                                                        @endif
                                                                                    </b>
                                                                                @endif

                                                                            @endif
                                                                        @endforeach
                                                                    </small>

                                                                </h6>
                                                            </div>
                                                            @endif
                                                        </div>

                                                        <!-- Compact Modern Action Buttons -->
                                                        <div class="form-row mt-0">
                                                            <div class="form-group mr-1 mb-1">
                                                                <!-- Syarat Program Button -->
                                                                @if (session()->get('jenprog') == 'spm')
                                                                    <a href="{{ url('ProgramPengajian/modalSyarat/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->kod_Program) }}"
                                                                        id="kelayakanMinimum_Modal" data-toggle="modal" data-target="#subjek_{{ $PROGRAM->kod_Program }}"
                                                                        class="btn-compact btn-compact-primary" data-tooltip="Syarat Program">
                                                                        <div class="btn-compact-icon">
                                                                            <i class="fas fa-book"></i>
                                                                        </div>
                                                                        <span class="btn-compact-text">Syarat Program</span>
                                                                    </a>
                                                                @elseif (session()->get('jenprog') == 'stpm')
                                                                    @if($PROGRAM->kategori_Pengajian=='G' || $PROGRAM->kategori_Pengajian=='E' || $PROGRAM->kategori_Pengajian=='F')
                                                                        <a href="{{ url('ProgramPengajian/modalSyaratdiploma/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->jensetaraf . '/' . $PROGRAM->kod_Program) }}"
                                                                            id="kelayakanMinimum_Modal" data-toggle="modal" data-target="#subjek_{{ $PROGRAM->kod_Program }}"
                                                                            class="btn-compact btn-compact-primary" data-tooltip="Syarat Program">
                                                                            <div class="btn-compact-icon">
                                                                                <i class="fas fa-book"></i>
                                                                            </div>
                                                                            <span class="btn-compact-text">Syarat Program</span>
                                                                        </a>
                                                                    @else
                                                                        <a href="{{ url('ProgramPengajian/modalSyarat/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->kod_Program) }}"
                                                                            id="kelayakanMinimum_Modal" data-toggle="modal" data-target="#subjek_{{ $PROGRAM->kod_Program }}"
                                                                            class="btn-compact btn-compact-primary" data-tooltip="Syarat Program">
                                                                            <div class="btn-compact-icon">
                                                                                <i class="fas fa-book"></i>
                                                                            </div>
                                                                            <span class="btn-compact-text">Syarat Program</span>
                                                                        </a>
                                                                    @endif
                                                                @endif
                                                            </div>
                                                            <div class="form-group mt-0">
                                                                <!-- Yuran Pengajian Button -->
                                                                <a href="javascript:void(0)" data-toggle="modal" data-target="#yuran-pengajian"
                                                                    class="btn-compact btn-compact-success" data-tooltip="Yuran Pengajian">
                                                                    <div class="btn-compact-icon">
                                                                        <i class="fas fa-dollar-sign"></i>
                                                                    </div>
                                                                    <span class="btn-compact-text">Yuran Pengajian</span>
                                                                </a>
                                                            </div>
                                                        </div>

                                                        <div class="form-row mt-3">
                                                            <div class="form-group mr-1 mb-1">
                                                                <!-- Kampus Button -->
                                                                <a href="javascript:void(0)" data-toggle="modal" data-target="#kampus__{{ $PROGRAM->kod_Program }}"
                                                                    class="btn-compact btn-compact-info" data-tooltip="Kampus">
                                                                    <div class="btn-compact-icon">
                                                                        <i class="fas fa-map-marked-alt"></i>
                                                                    </div>
                                                                    <span class="btn-compact-text">Kampus</span>
                                                                </a>
                                                            </div>
                                                            <div class="form-group">
                                                                <!-- Laluan Kerjaya Button -->
                                                                <a href="javascript:void(0)" data-toggle="modal" data-target="#laluan-kerjaya_{{ $PROGRAM->kod_Program }}"
                                                                    class="btn-compact btn-compact-warning" data-tooltip="Laluan Kerjaya">
                                                                    <div class="btn-compact-icon">
                                                                        <i class="fas fa-briefcase"></i>
                                                                    </div>
                                                                    <span class="btn-compact-text">Laluan Kerjaya</span>
                                                                </a>
                                                            </div>
                                                        </div>

                                                       @if(substr(Request::route('kodkatag'),0,1)=='G')
                                                        <div class="form-row mt-3">
                                                            <div class="form-group mr-1 mb-1">
                                                                <!-- Bidang NEC Button -->
                                                                <a href="javascript:void(0)" data-toggle="modal" data-target="#bidangNEC_{{ $PROGRAM->kod_Program }}"
                                                                    class="btn-compact btn-compact-secondary" data-tooltip="Bidang NEC">
                                                                    <div class="btn-compact-icon">
                                                                        <i class="fas fa-bullseye"></i>
                                                                    </div>
                                                                    <span class="btn-compact-text">Bidang NEC</span>
                                                                </a>
                                                            </div>
                                                            <div class="form-group">
                                                                <!-- Empty space for alignment -->
                                                            </div>
                                                        </div>
                                                        @endif








                                                        <ul class="list-unstyled mb-0 flex-wrap">
                                                           {{--  <li class="mt-2 list-inline-item"
                                                                class="btn btn-primary mr-2 mb-2" data-toggle="tooltip"
                                                                data-placement="top" title="Kampus" alt=""><a
                                                                    href="javascript:void(0)" data-toggle="modal"
                                                                    data-target="#kampus__{{ $PROGRAM->kod_Program }}"
                                                                    class="btn btn-icon btn-pills btn-soft-primary tooltip-hide"><i
                                                                        data-feather="map-pin" class="icons"
                                                                        style="color: white"></i></a>
                                                            </li> --}}
                                                           {{--  <li class="mt-2 list-inline-item"
                                                                class="btn btn-primary mr-2 mb-2" data-toggle="tooltip"
                                                                data-placement="top" title="Yuran Pengajian"
                                                                alt="">
                                                                <a href="javascript:void(0)" data-toggle="modal"
                                                                    data-target="#yuran-pengajian"
                                                                    class="btn btn-icon btn-pills btn-soft-primary"><i
                                                                        data-feather="dollar-sign" class="icons"
                                                                        style="color: white"></i></a>
                                                            </li> --}}
                                                            @if (session()->get('jenprog') == 'spm')
                                                              {{--   <li class="mt-2 list-inline-item"
                                                                    class="btn btn-primary mr-2 mb-2"
                                                                    data-toggle="tooltip" data-placement="top"
                                                                    title="Syarat Khas Program" alt=""><a
                                                                        href="{{ url('ProgramPengajian/modalSyarat/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->kod_Program) }}"
                                                                        id="kelayakanMinimum_Modal"
                                                                        name="kelayakanMinimum_Modal" data-toggle="modal"
                                                                        data-target="#subjek_{{ $PROGRAM->kod_Program }}"
                                                                        class="btn btn-icon btn-pills btn-soft-primary"><i
                                                                            data-feather="book" class="icons"
                                                                            style="color: white"></i></a>
                                                                </li> --}}
                                                                <div class="modal fade kelayakanMinimum_Modal"
                                                                    id="subjek_{{ $PROGRAM->kod_Program }}"
                                                                    tabindex="-1" role="dialog"
                                                                    aria-labelledby="syarat-program-title"
                                                                    aria-hidden="true">
                                                                    <div class="modal-dialog modal-dialog-centered modal-llgg"
                                                                        role="document">
                                                                        <div
                                                                            class="modal-content rounded shadow-lg border-0 overflow-hidden">
                                                                            <button type="button"
                                                                                class="close float-right mr-2"
                                                                                data-dismiss="modal" aria-label="Close"
                                                                                style="display:flex; justify-content:flex-end; width:100%; padding:0.9rem; margin-top: -0.8rem;">
                                                                                <span aria-hidden="true">&times;</span>
                                                                            </button>
                                                                            <div class="modal-body" style="height: 90vh; overflow-y: auto;">
                                                                                <div class="text-left">
                                                                                    <h4 class="text-center"><b>Syarat
                                                                                            Program</b>
                                                                                    </h4>
                                                                                    <div class="container mt-100 mt-60">
                                                                                        <div class="row">
                                                                                            <div class="col-12">
                                                                                                <ul class="nav nav-pills shadow flex-column flex-sm-row mb-0 p-1 bg-white rounded overflow-hidden"
                                                                                                    id="pills-tab"
                                                                                                    role="tablist">
                                                                                                    <!--Syarat Am Tab-->
                                                                                                    <li
                                                                                                        class="nav-item col-sm-6">
                                                                                                        <a class="nav-link py-2 active rounded"
                                                                                                            id="syarat-am-tab-{{ $PROGRAM->kod_Program }}"
                                                                                                            data-toggle="pill"
                                                                                                            href="#syarat-am-{{ $PROGRAM->kod_Program }}"
                                                                                                            role="tab"
                                                                                                            aria-controls="syarat-am"
                                                                                                            aria-selected="false">
                                                                                                            <div
                                                                                                                class="text-center">
                                                                                                                <h6
                                                                                                                    class="mb-0">
                                                                                                                    Syarat Am</h6>
                                                                                                            </div>
                                                                                                        </a>
                                                                                                    </li>

                                                                                                    <!--Syarat Khas Tab-->
                                                                                                    <li
                                                                                                        class="nav-item col-sm-6">
                                                                                                        <a class="nav-link py-2 rounded"
                                                                                                            id="syarat-Khas-tab-{{ $PROGRAM->kod_Program }}"
                                                                                                            data-toggle="pill"
                                                                                                            href="#syarat-Khas-{{ $PROGRAM->kod_Program }}"
                                                                                                            role="tab"
                                                                                                            aria-controls="syarat-Khas"
                                                                                                            aria-selected="false">
                                                                                                            <div
                                                                                                                class="text-center">
                                                                                                                <h6
                                                                                                                    class="mb-0">
                                                                                                                    Syarat Khas
                                                                                                                </h6>
                                                                                                            </div>
                                                                                                        </a>
                                                                                                    </li>
                                                                                                </ul>

                                                                                                <div class="tab-content"
                                                                                                    id="pills-tabContent"
                                                                                                    style="padding-top: 2rem!important">
                                                                                                    <!--Paparan Syarat Am Tab-->
                                                                                                    <div class="card border-0 tab-pane fade show active"
                                                                                                        id="syarat-am-{{ $PROGRAM->kod_Program }}"
                                                                                                        role="tabpanel"
                                                                                                        aria-labelledby="syarat-am-tab-{{ $PROGRAM->kod_Program }}">

                                                                                                        @include('programPengajian.syarat_am_spm')
                                                                                                        {{-- @foreach ($SYARAT_AM as $syaratAM_SPM)
                                                                                                            @if ($syaratAM_SPM->syaratAm_kodProgram == $PROGRAM->kod_Program)
                                                                                                                <b
                                                                                                                    class="text-muted mb-0">{!! $syaratAM_SPM->USA_SYARAT !!}</b>
                                                                                                            @endif
                                                                                                        @endforeach --}}

                                                                                                    </div>

                                                                                                    <!--Paparan Syarat khas Tab-->
                                                                                                    <div class="card border-0 tab-pane fade"
                                                                                                        id="syarat-Khas-{{ $PROGRAM->kod_Program }}"
                                                                                                        role="tabpanel"
                                                                                                        aria-labelledby="syarat-Khas-tab-{{ $PROGRAM->kod_Program }}">
                                                                                                        <div class="text-muted contentLoad"
                                                                                                            style="font-weight: bold">
                                                                                                            <div
                                                                                                                align="center">
                                                                                                                <div
                                                                                                                    class="loader-spinner text-center">
                                                                                                                </div>
                                                                                                                <h4>Sila
                                                                                                                    tunggu
                                                                                                                    sebentar...
                                                                                                                </h4>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>



                                                            @elseif (session()->get('jenprog') == 'stpm')



                                                           {{--  @if($PROGRAM->kategori_Pengajian=='G' || $PROGRAM->kategori_Pengajian=='E' || $PROGRAM->kategori_Pengajian=='F')
                                                            <li class="mt-2 list-inline-item"
                                                                class="btn btn-primary mr-2 mb-2"
                                                                data-toggle="tooltip" data-placement="top"
                                                                title="Syarat Khas Program" alt=""><a
                                                                    href="{{ url('ProgramPengajian/modalSyaratdiploma/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->jensetaraf . '/' . $PROGRAM->kod_Program) }}"
                                                                    id="kelayakanMinimum_Modal"
                                                                    name="kelayakanMinimum_Modal" data-toggle="modal"
                                                                    data-target="#subjek_{{ $PROGRAM->kod_Program }}"
                                                                    class="btn btn-icon btn-pills btn-soft-primary"><i
                                                                        data-feather="book" class="icons"
                                                                        style="color: white"></i></a>
                                                            </li>
                                                            @else
                                                            <li class="mt-2 list-inline-item"
                                                                class="btn btn-primary mr-2 mb-2"
                                                                data-toggle="tooltip" data-placement="top"
                                                                title="Syarat Khas Program" alt=""><a
                                                                    href="{{ url('ProgramPengajian/modalSyarat/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->kod_Program) }}"
                                                                    id="kelayakanMinimum_Modal"
                                                                    name="kelayakanMinimum_Modal" data-toggle="modal"
                                                                    data-target="#subjek_{{ $PROGRAM->kod_Program }}"
                                                                    class="btn btn-icon btn-pills btn-soft-primary"><i
                                                                        data-feather="book" class="icons"
                                                                        style="color: white"></i></a>
                                                            </li>
                                                            @endif --}}

                                                            <div class="modal fade kelayakanMinimum_Modal"
                                                                id="subjek_{{ $PROGRAM->kod_Program }}"
                                                                tabindex="-1" role="dialog"
                                                                aria-labelledby="syarat-program-title"
                                                                aria-hidden="true">
                                                                <div class="modal-dialog modal-dialog-centered modal-llgg"
                                                                    role="document" >
                                                                    <div
                                                                        class="modal-content rounded shadow-lg border-0 overflow-hidden">
                                                                        <button type="button"
                                                                            class="close float-right mr-2"
                                                                            data-dismiss="modal" aria-label="Close"
                                                                            style="display:flex; justify-content:flex-end; width:100%; padding:0.9rem; margin-top: -0.8rem;">
                                                                            <span aria-hidden="true">&times;</span>
                                                                        </button>
                                                                        <div class="modal-body" style="height: 90vh; overflow-y: auto;">
                                                                            <div class="text-left">
                                                                                <h4 class="text-center"><b>Syarat
                                                                                        Program</b>
                                                                                </h4>
                                                                                <div class="container mt-100 mt-60">
                                                                                    <div class="row">
                                                                                        <div class="col-12">
                                                                                            <ul class="nav nav-pills shadow flex-column flex-sm-row mb-0 p-1 bg-white rounded overflow-hidden"
                                                                                                id="pills-tab"
                                                                                                role="tablist">
                                                                                                <!--Syarat Am Tab-->
                                                                                                <li @if (substr($PROGRAM->bidang_NEC, 0, 2) == '01') class="nav-item col-4" @else class="nav-item col-6" @endif>
                                                                                                    <a class="nav-link py-2 active rounded"
                                                                                                        id="syarat-am-tab-{{ $PROGRAM->kod_Program }}"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-am-{{ $PROGRAM->kod_Program }}"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-am"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Am</h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>

                                                                                                @if (substr($PROGRAM->bidang_NEC, 0, 2) == '01')
                                                                                                <li {{ $PROGRAM->kod_Program == 'UM6143001' ? 'hidden' : null }}
                                                                                                    class="nav-item col-4">
                                                                                                    <a class="nav-link py-2 rounded"
                                                                                                        id="syarat-pendidikan-tab-{{ $PROGRAM->kod_Program }}"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-pendidikan-{{ $PROGRAM->kod_Program }}"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-pendidikan"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Pendidikan</h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>
                                                                                                @endif


                                                                                                <!--Syarat Khas Tab-->
                                                                                                <li @if (substr($PROGRAM->bidang_NEC, 0, 2) == '01') class="nav-item col-4" @else class="nav-item col-6" @endif>
                                                                                                    <a class="nav-link py-2 rounded"
                                                                                                        id="syarat-Khas-tab-{{ $PROGRAM->kod_Program }}"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-Khas-{{ $PROGRAM->kod_Program }}"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-Khas"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Khas
                                                                                                            </h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>
                                                                                            </ul>

                                                                                            <div class="tab-content"
                                                                                                id="pills-tabContent"
                                                                                                style="padding-top: 2rem!important">
                                                                                                <!--Paparan Syarat Am Tab-->
                                                                                                <div class="card border-0 tab-pane fade show active"
                                                                                                    id="syarat-am-{{ $PROGRAM->kod_Program }}"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-am-tab-{{ $PROGRAM->kod_Program }}">

                                                                                                    @if($PROGRAM->kategori_Pengajian=='G')
																										@include('programPengajian.syarat_am_diploma_g')
                                                                                                    @elseif($PROGRAM->kategori_Pengajian=='E')
																										@include('programPengajian.syarat_am_diploma_e')
                                                                                                    @elseif($PROGRAM->kategori_Pengajian=='F')
																										@include('programPengajian.syarat_am_diploma_f')
                                                                                                    @else
																										@include('programPengajian.syarat_am_stpm')
                                                                                                    @endif

                                                                                                </div>

                                                                                                @if (substr($PROGRAM->bidang_NEC, 0, 2) == '01')
                                                                                                <div class="card border-0 tab-pane fade"
                                                                                                    id="syarat-pendidikan-{{ $PROGRAM->kod_Program }}"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-pendidikan-tab-{{ $PROGRAM->kod_Program }}">

                                                                                                    @include('programPengajian.syarat_am_pendidikan')
                                                                                                </div>
                                                                                                @endif


                                                                                                <!--Paparan Syarat khas Tab-->
                                                                                                <div class="card border-0 tab-pane fade"
                                                                                                    id="syarat-Khas-{{ $PROGRAM->kod_Program }}"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-Khas-tab-{{ $PROGRAM->kod_Program }}">
                                                                                                    <div class="text-muted contentLoad"
                                                                                                        style="font-weight: bold">
                                                                                                        <div
                                                                                                            align="center">
                                                                                                            <div
                                                                                                                class="loader-spinner text-center">
                                                                                                            </div>
                                                                                                            <h4>Sila
                                                                                                                tunggu
                                                                                                                sebentar...
                                                                                                            </h4>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>





                                                                {{-- <li class="mt-2 list-inline-item"
                                                                    class="btn btn-primary mr-2 mb-2"
                                                                    data-toggle="tooltip" data-placement="top"
                                                                    title="Syarat Khas Program" alt=""><a
                                                                        href="javascript:void(0)" data-toggle="modal"
                                                                        data-target="#kelayakanMinimum_Modal{{ $PROGRAM->kod_Program }}"
                                                                        class="btn btn-icon btn-pills btn-soft-primary"><i
                                                                            data-feather="book" class="icons"
                                                                            style="color: white"></i></a>
                                                                </li> --}}
                                                            @endif
                                                            {{-- <li class="mt-2 list-inline-item"
                                                                class="btn btn-primary mr-2 mb-2" data-toggle="tooltip"
                                                                data-placement="top" title="Laluan Kerjaya"
                                                                alt=""><a href="javascript:void(0)"
                                                                    data-toggle="modal"
                                                                    data-target="#laluan-kerjaya_{{ $PROGRAM->kod_Program }}"
                                                                    class="btn btn-icon btn-pills btn-soft-primary"><i
                                                                        data-feather="briefcase" class="icons"
                                                                        style="color: white"></i></a>
                                                            </li> --}}

                                                            {{-- BIDANG NEC --}}
                                                            {{-- @if(substr(Request::route('kodkatag'),0,1)=='G')
                                                            <li class="mt-2 list-inline-item"
                                                                class="btn btn-primary mr-2 mb-2" data-toggle="tooltip"
                                                                data-placement="top" title="Bidang NEC"
                                                                alt=""><a href="javascript:void(0)"
                                                                    data-toggle="modal"
                                                                    data-target="#bidangNEC_{{ $PROGRAM->kod_Program }}"
                                                                    class="btn btn-icon btn-pills btn-soft-primary"><i
                                                                        data-feather="target" class="icons"
                                                                        style="color: white"></i></a>
                                                            </li>
                                                            @endif --}}

                                                            <li class="mt-0 mb-4 list-inline-item"
                                                                style="float: right; padding-top: 0.5rem">
                                                                <a type="button" class="text-dark" data-toggle="modal"
                                                                    data-target="#maklumatProgram__{{ $PROGRAM->kod_Program }}">
                                                                    Maklumat program
                                                                    <i data-feather="arrow-right" class="icons"></i></a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>

                                                <!--end col-->
                                            </div>
                                            <!--end row-->
                                        </div>
                                        <!--end blog post-->
                                    </div>
                                    @include('programPengajian.modal')
                                @endforeach
                            @else
                                @include('pageLock.tiadaMaklumat')
                            @endif

                            <!-- PAGINATION START -->
                            <div class="col-12 mt-4 pt-2">
                                {!! $SENARAI_PROGRAM->links('programPengajian.list-paginator') !!}
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>
    <script src="{{ asset('/assets/js/range-slider.js') }}"></script>

    <!-- Compact Modern Button Styles -->
    <style>
        /* Compact Button Base Styling - Executive Modern Design */
        .btn-compact {
            display: inline-flex;
            align-items: center;
            padding: 10px 18px;
            border-radius: 4px;
            text-decoration: none !important;
            font-weight: 600;
            font-size: 13px;
            border: 1px solid;
            background: #ffffff;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            width: 140px;
            height: 42px;
            justify-content: center;
            letter-spacing: 0.5px;
        }

        .btn-compact:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            text-decoration: none !important;
        }

        .btn-compact:active {
            transform: translateY(0) scale(0.98);
            transition: transform 0.1s;
        }

        .btn-compact:focus {
            text-decoration: none !important;
            outline: none;
        }

        /* Button Icon */
        .btn-compact-icon {
            margin-right: 6px;
            font-size: 14px;
            transition: transform 0.3s ease;
        }

        .btn-compact:hover .btn-compact-icon {
            transform: scale(1.1) rotate(5deg);
        }

        /* Button Text */
        .btn-compact-text {
            font-weight: 600;
            letter-spacing: 0.3px;
            white-space: nowrap;
        }

        /* Executive Modern Color Variants */
        .btn-compact-primary {
            border-color: #2c3e50;
            color: #2c3e50;
            background: #ffffff;
        }

        .btn-compact-primary:hover {
            background: #2c3e50;
            color: white !important;
            border-color: #2c3e50;
            text-decoration: none !important;
            box-shadow: 0 4px 15px rgba(44, 62, 80, 0.3);
        }

        .btn-compact-success {
            border-color: #27ae60;
            color: #27ae60;
            background: #ffffff;
        }

        .btn-compact-success:hover {
            background: #27ae60;
            color: white !important;
            border-color: #27ae60;
            text-decoration: none !important;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }

        .btn-compact-info {
            border-color: #3498db;
            color: #3498db;
            background: #ffffff;
        }

        .btn-compact-info:hover {
            background: #3498db;
            color: white !important;
            border-color: #3498db;
            text-decoration: none !important;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .btn-compact-warning {
            border-color: #f39c12;
            color: #f39c12;
            background: #ffffff;
        }

        .btn-compact-warning:hover {
            background: #f39c12;
            color: white !important;
            border-color: #f39c12;
            text-decoration: none !important;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        .btn-compact-secondary {
            border-color: #95a5a6;
            color: #95a5a6;
            background: #ffffff;
        }

        .btn-compact-secondary:hover {
            background: #95a5a6;
            color: white !important;
            border-color: #95a5a6;
            text-decoration: none !important;
            box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
        }

        /* Simplified Tooltip Styling */
        .btn-compact[data-tooltip] {
            position: relative;
        }

        .btn-compact[data-tooltip]:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 120%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateX(-50%) translateY(10px); }
            to { opacity: 1; transform: translateX(-50%) translateY(0); }
        }

        /* Action Buttons Container */
        .action-buttons-container {
            margin: 0;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .btn-compact {
                padding: 8px 14px;
                font-size: 12px;
                width: 125px;
                height: 38px;
                border-radius: 3px;
            }

            .btn-compact-icon {
                font-size: 12px;
                margin-right: 4px;
            }

            .btn-compact-text {
                font-size: 11px;
            }
        }

        @media (max-width: 480px) {
            .btn-compact {
                width: 110px;
                height: 36px;
                font-size: 11px;
                padding: 6px 12px;
            }

            .btn-compact-text {
                font-size: 10px;
            }
        }

        /* Loading State */
        .btn-compact.loading {
            pointer-events: none;
            opacity: 0.7;
        }

        .btn-compact.loading .btn-compact-icon {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Focus States for Accessibility */
        .btn-compact:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
        }

        .btn-compact-success:focus {
            box-shadow: 0 0 0 3px rgba(25, 135, 84, 0.25);
        }

        .btn-compact-info:focus {
            box-shadow: 0 0 0 3px rgba(13, 202, 240, 0.25);
        }

        .btn-compact-warning:focus {
            box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.25);
        }

        .btn-compact-secondary:focus {
            box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.25);
        }

        /* Remove any pseudo-elements that might cause black bars */
        .btn-compact::before {
            display: none;
        }

        .btn-compact::after {
            display: none;
        }

        /* Ensure no underlines or text decorations */
        .btn-compact,
        .btn-compact:hover,
        .btn-compact:focus,
        .btn-compact:active,
        .btn-compact:visited {
            text-decoration: none !important;
            border-bottom: none !important;
        }
    </style>

    <!-- Compact Button JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add interactive effects to compact buttons
            const compactButtons = document.querySelectorAll('.btn-compact');

            compactButtons.forEach(button => {
                // Add click animation
                button.addEventListener('click', function(e) {
                    // Add loading state
                    const icon = this.querySelector('.btn-compact-icon i');
                    const originalIcon = icon.className;

                    this.classList.add('loading');
                    icon.className = 'fas fa-spinner';

                    // Remove loading state after modal opens
                    setTimeout(() => {
                        this.classList.remove('loading');
                        icon.className = originalIcon;
                    }, 800);
                });

                // Add stagger animation on page load
                button.style.opacity = '0';
                button.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    button.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
                    button.style.opacity = '1';
                    button.style.transform = 'translateY(0)';
                }, Math.random() * 300 + 100);
            });

            // Add intersection observer for scroll animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const buttons = entry.target.querySelectorAll('.btn-compact');
                        buttons.forEach((button, index) => {
                            setTimeout(() => {
                                button.style.animation = 'fadeInUp 0.6s ease forwards';
                            }, index * 100);
                        });
                    }
                });
            }, observerOptions);

            // Observe button containers
            document.querySelectorAll('.action-buttons-container').forEach(container => {
                observer.observe(container);
            });
        });

        // Add CSS animation keyframes
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
@endsection
