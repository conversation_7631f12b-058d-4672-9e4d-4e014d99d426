    <div class="row" style="font-size:.875rem;">
    
        <div class="col-xl-7 col-lg-6 col-md-12 col-sm-12">
            <table class="table table-bordered table-sm" width="100%" cellspacing="0">
                <thead class="thead-light">
                    <tr>
                        <th colspan="5" style="text-align:center; color:#000;">(A) 5 Mata Pelajaran Utama Mengikut Aliran</th>
                    </tr>
                </thead>
            
                <thead class="thead-dark" style="text-align:center;">
                    <tr>
                        <th colspan="2"><PERSON> Pelajaran</th>
                        <th width="130px" rowspan="2" style="vertical-align: middle;">Gred</th>
                        <th width="70px"  rowspan="2" style="vertical-align: middle;">Merit</th>
                    </tr>
					
                    <tr>
                        <th>Aliran Sains </th>
						<th>Aliran Sastera</th>
                    </tr>
					
                </thead>
            
                <tbody style="color: #000;">
    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">Matematik</td>
						<td style="vertical-align: middle; font-weight:bold;">Bahasa Melayu</td>
    
                        <td>
                            <select name="GRDGRED1" id="GRDGRED1" class="form-control form-control-sm" style="border: none;" onchange="kira()">
                                <option value="" selected>Sila Pilih</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
    
                        <td>
                            {!! Form::text('MRKMERIT1', 0, ['id'=>'MRKMERIT1', 'class' => 'form-control form-control-sm', 'maxlength' => 2, 'disabled'=> 'disabled', 'style'=>'border: none; text-align:center; background: transparent;']) !!}
                        </td>
                    </tr>
    
    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">Matematik Tambahan</td>
						<td style="vertical-align: middle; font-weight:bold;">Matematik</td>
    
                        <td>
                            <select name="GRDGRED2" id="GRDGRED2" class="form-control form-control-sm" style="border: none;" onchange="kira()">
                                <option value="" selected>Sila Pilih</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
    
                        <td>
                            {!! Form::text('MRKMERIT2', 0, ['id'=>'MRKMERIT2', 'class' => 'form-control form-control-sm', 'maxlength' => 2, 'disabled'=> 'disabled', 'style'=>'border: none; text-align:center; background: transparent;']) !!}
                        </td>
                    </tr>
                    
    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">Fizik</td>
						<td style="vertical-align: middle; font-weight:bold;">Sains</td>
    
                        <td>
                            <select name="GRDGRED3" id="GRDGRED3" class="form-control form-control-sm" style="border: none;" onchange="kira()">
                                <option value="" selected>Sila Pilih</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
    
                        <td>
                            {!! Form::text('MRKMERIT3', 0, ['id'=>'MRKMERIT3', 'class' => 'form-control form-control-sm', 'maxlength' => 2, 'disabled'=> 'disabled', 'style'=>'border: none; text-align:center; background: transparent;']) !!}
                        </td>
                    </tr>
    
    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">Kimia</td>
						<td style="vertical-align: middle; font-weight:bold;">Sejarah</td>
    
                        <td>
                            <select name="GRDGRED4" id="GRDGRED4" class="form-control form-control-sm" style="border: none;" onchange="kira()">
                                <option value="" selected>Sila Pilih</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
    
                        <td>
                            {!! Form::text('MRKMERIT4', 0, ['id'=>'MRKMERIT4', 'class' => 'form-control form-control-sm', 'maxlength' => 2, 'disabled'=> 'disabled', 'style'=>'border: none; text-align:center; background: transparent;']) !!}
                        </td>
                    </tr>
    
    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">Biologi atau salah satu (1) mata pelajaran lain <span style="color:#F00;"> *</span></td>
						<td style="vertical-align: middle; font-weight:bold;">Pendidikan Islam atau salah satu (1) mata pelajaran lain  <span style="color:#F00;"> **</span></td>
    
                        <td>
                            <select name="GRDGRED5" id="GRDGRED5" class="form-control form-control-sm" style="border: none;" onchange="kira()">
                                <option value="" selected>Sila Pilih</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
    
                        <td>
                            {!! Form::text('MRKMERIT5', 0, ['id'=>'MRKMERIT5', 'class' => 'form-control form-control-sm', 'maxlength' => 2, 'disabled'=> 'disabled', 'style'=>'border: none; text-align:center; background: transparent;']) !!}
                        </td>
                    </tr>
                    
                    <tr>
                        <td colspan="3" style="vertical-align: middle; text-align:right; font-weight:bold;">Jumlah</td>
    
                        <td>
                            {!! Form::text('TOTAL1', 0, ['id'=>'TOTAL1', 'class' => 'form-control form-control-sm', 'maxlength' => 2, 'disabled'=> 'disabled', 'style'=>'border: none; text-align:center; background: transparent;']) !!}
                        </td>
                    </tr>
    
                </tbody>
            
            </table>
    
    
            {{-- ************************************************************************************************************************************** --}}
            {{-- ************************************************************************************************************************************** --}}
            
    
            <table class="table table-bordered table-sm" width="100%" cellspacing="0">
    
                <thead class="thead-light">
                    <tr>
                        <th colspan="3" style="text-align:center; color:#000;">(B) 3 Mata Pelajaran Terbaik Lain</th>
                    </tr>
                </thead>
            
                <thead class="thead-dark" style="text-align:center;">
                    <tr>
                        <th>Mata Pelajaran</th>
                        <th width="130px">Gred</th>
                        <th width="70px">Merit</th>
                    </tr>
                </thead>
            
                <tbody style="color: #000;">
    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">Subjek SPM  1</td>
    
                        <td>
                            <select name="GRDGRED6" id="GRDGRED6" class="form-control form-control-sm" style="border: none;" onchange="kira2()">
                                <option value="" selected>Sila Pilih</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
    
                        <td>
                            {!! Form::text('MRKMERIT6', 0, ['id'=>'MRKMERIT6', 'class' => 'form-control form-control-sm', 'maxlength' => 2, 'disabled'=> 'disabled', 'style'=>'border: none; text-align:center; background: transparent;']) !!}
                        </td>
                    </tr>
    
    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">Subjek SPM  2</td>
    
                        <td>
                            <select name="GRDGRED7" id="GRDGRED7" class="form-control form-control-sm" style="border: none;" onchange="kira2()">
                                <option value="" selected>Sila Pilih</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
    
                        <td>
                            {!! Form::text('MRKMERIT7', 0, ['id'=>'MRKMERIT7', 'class' => 'form-control form-control-sm', 'maxlength' => 2, 'disabled'=> 'disabled', 'style'=>'border: none; text-align:center; background: transparent;']) !!}
                        </td>
                    </tr>
                    
    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">Subjek SPM  3</td>
    
                        <td>
                            <select name="GRDGRED8" id="GRDGRED8" class="form-control form-control-sm" style="border: none;" onchange="kira2()">
                                <option value="" selected>Sila Pilih</option>   
                                    @foreach($merit_gred_spm as $merit_gred)
                                        <option value="{{$merit_gred->kodspmgred}}">{{$merit_gred->ketspmgred}}</option>
                                    @endforeach   
                            </select> 
                        </td>
    
                        <td>
                            {!! Form::text('MRKMERIT8', 0, ['id'=>'MRKMERIT8', 'class' => 'form-control form-control-sm', 'maxlength' => 2, 'disabled'=> 'disabled', 'style'=>'border: none; text-align:center; background: transparent;']) !!}
                        </td>
                    </tr>
    
                    <tr>
                        <td colspan="2" style="vertical-align: middle; text-align:right; font-weight:bold;">Jumlah</td>
    
                        <td>
                            {!! Form::text('TOTAL2', 0, ['id'=>'TOTAL2', 'class' => 'form-control form-control-sm', 'maxlength' => 2, 'disabled'=> 'disabled', 'style'=>'border: none; text-align:center; background: transparent;']) !!}
                        </td>
                    </tr>
    
                </tbody>
            
            </table>
    
            {{-- ************************************************************************************************************************************** --}}
            {{-- ************************************************************************************************************************************** --}}
    
            <table class="table table-bordered table-sm" width="100%" cellspacing="0">
    
                <thead class="thead-light">
                    <tr>
                        <th colspan="2" style="text-align:center; color:#000;">(C) Pengiraan</th>
                    </tr>
                </thead>
            
                <tbody style="color: #000;">
    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">Jumlah markah 5 mata pelajaran utama (A) &emsp; [ (A / 90) x 90 ]</td>
    
                        <td width="70px">
                            {!! Form::text('MRKALL1', 0, ['id'=>'MRKALL1', 'class' => 'form-control form-control-sm', 'maxlength' => 5, 'disabled'=> 'disabled', 'style'=>'border: none; text-align:center; background: transparent;']) !!}
                        </td>
                    </tr>
    
    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">Jumlah markah 3 pelajaran terbaik (B) &emsp; [ (B / 54) x 30 ] </td>
    
                        <td>
                            {!! Form::text('MRKALL2', 0, ['id'=>'MRKALL2', 'class' => 'form-control form-control-sm', 'maxlength' => 5, 'disabled'=> 'disabled', 'style'=>'border: none; text-align:center; background: transparent;']) !!}
                        </td>
                    </tr>
                    
                    <tr>
                        <td style="vertical-align: middle; font-weight:bold;">Jumlah markah akademik (90%) &emsp; [ (A+B) / 120 x 90 ]</td>
    
                        <td>
                            {!! Form::text('MRKALL3', 0, ['id'=>'MRKALL3', 'class' => 'form-control form-control-sm', 'maxlength' => 5, 'disabled'=> 'disabled', 'style'=>'border: none; text-align:center; background: transparent;']) !!}
                        </td>
                    </tr>
    
                    <tr class="bg-warning">
                        <td style="vertical-align: middle; font-weight:bold;">Markah Ko-kurikulum (10%)</td>
    
                        <td>
                            {!! Form::text('MRKALL4', 0, ['id'=>'MRKALL4', 'class' => 'form-control form-control-sm maxmin', 'maxlength' => 5, 'min' => 0, 'max' => 10, 'onkeypress'=>'return isNumber(event)', 'style'=>'text-align:center;']) !!}
                        </td>
                    </tr>
    
                    <tr class="bg-info">
                        <td colspan="1" style="vertical-align: middle; text-align:right; font-weight:bold;">Jumlah (Markah Merit)</td>
    
                        <td>
                            {!! Form::text('TOTAL3', 0, ['id'=>'TOTAL3', 'class' => 'form-control form-control-sm', 'maxlength' => 5, 'disabled'=> 'disabled', 'style'=>'border: none; text-align:center; background: transparent;']) !!}
                        </td>
                    </tr>  
    
                </tbody>
            
            </table>
    
        </div>
		
        <div class="col-xl-5 col-lg-6 col-md-12 col-sm-12">
			<table class="table table-borderless table-sm" width="100%" cellspacing="0">
				<tbody style="color: #000;">
					<tr>
						<td style="font-weight:bold; color:#F00; font-size:20px;">*</td>
						<td style="vertical-align: middle; font-weight:bold;">
						Additional Science/ Pengajian Kejuruteraan Awam/ Elektrik & Elektronik/ Mekanikal/ 
						Reka cipta/ Lukisan Kejuruteraan/ Grafik Komunikasi Teknikal/ Teknologi Kejuruteraan/ 
						Asas Kelestarian/ Ekonomi Rumah Tangga/ Sains Rumah Tangga/ Sains Pertanian/ Pertanian/ 
						Information and Communication Technology/ Sains Komputer/ Prinsip Perakaunan/ Perdagangan/ 
						Perniagaan/ Ekonomi Asas/ Ekonomi/ Pengetahuan Sains Sukan/ Sains Sukan/ Pengurusan Makanan;
						</td>
					</tr>
				</tbody>
			</table>
			
			<table class="table table-borderless table-sm" width="100%" cellspacing="0">
				<tbody style="color: #000;">
					<tr>
						<td style="font-weight:bold; color:#F00; font-size:20px;">**</td>
						<td style="vertical-align: middle; font-weight:bold;">
						Pendidikan Syari’ah Islamiah / Pengajian Al-Quran & Al-Sunnah / Pendidikan Moral/ Hifz Al-Quran/ 
						Maharat Al-Quran/ Turath Al-Quran dan Al-Sunnah/ Turath Bahasa Arab/ Usul Al-Din/ Al-Syariah/ 
						Al-Lughah Al-Arabiah Al-Mu’asirah/ Manahij Al-‘Ulum Al-Islamiah/ Al-Adab Wa Al-Balaghah
						</td>
					</tr>
				</tbody>
			</table>
			
        </div>
		
		
    </div>
            
    <script>
        function kira()
        {
            var vGRED1 = document.getElementById("GRDGRED1").selectedIndex;
            var vGRED2 = document.getElementById("GRDGRED2").selectedIndex;
            var vGRED3 = document.getElementById("GRDGRED3").selectedIndex;
            var vGRED4 = document.getElementById("GRDGRED4").selectedIndex;
            var vGRED5 = document.getElementById("GRDGRED5").selectedIndex;
            
            if(vGRED1==1){document.getElementById("MRKMERIT1").value = 18;}
            else if(vGRED1==2){document.getElementById("MRKMERIT1").value = 16;}
            else if(vGRED1==3){document.getElementById("MRKMERIT1").value = 14;}
            else if(vGRED1==4){document.getElementById("MRKMERIT1").value = 12;}
            else if(vGRED1==5){document.getElementById("MRKMERIT1").value = 10;}	
            else if(vGRED1==6){document.getElementById("MRKMERIT1").value = 8;}
            else if(vGRED1==7){document.getElementById("MRKMERIT1").value = 6;}	
            else if(vGRED1==8){document.getElementById("MRKMERIT1").value = 4;}
            else if(vGRED1==9){document.getElementById("MRKMERIT1").value = 2;}
            else {document.getElementById("MRKMERIT1").value = 0;}
            
            if(vGRED2==1){document.getElementById("MRKMERIT2").value = 18;}
            else if(vGRED2==2){document.getElementById("MRKMERIT2").value = 16;}
            else if(vGRED2==3){document.getElementById("MRKMERIT2").value = 14;}
            else if(vGRED2==4){document.getElementById("MRKMERIT2").value = 12;}
            else if(vGRED2==5){document.getElementById("MRKMERIT2").value = 10;}	
            else if(vGRED2==6){document.getElementById("MRKMERIT2").value = 8;}
            else if(vGRED2==7){document.getElementById("MRKMERIT2").value = 6;}	
            else if(vGRED2==8){document.getElementById("MRKMERIT2").value = 4;}
            else if(vGRED2==9){document.getElementById("MRKMERIT2").value = 2;}
            else {document.getElementById("MRKMERIT2").value = 0;}
            
            if(vGRED3==1){document.getElementById("MRKMERIT3").value = 18;}
            else if(vGRED3==2){document.getElementById("MRKMERIT3").value = 16;}
            else if(vGRED3==3){document.getElementById("MRKMERIT3").value = 14;}
            else if(vGRED3==4){document.getElementById("MRKMERIT3").value = 12;}
            else if(vGRED3==5){document.getElementById("MRKMERIT3").value = 10;}	
            else if(vGRED3==6){document.getElementById("MRKMERIT3").value = 8;}
            else if(vGRED3==7){document.getElementById("MRKMERIT3").value = 6;}	
            else if(vGRED3==8){document.getElementById("MRKMERIT3").value = 4;}
            else if(vGRED3==9){document.getElementById("MRKMERIT3").value = 2;}
            else {document.getElementById("MRKMERIT3").value = 0;}
            
            if(vGRED4==1){document.getElementById("MRKMERIT4").value = 18;}
            else if(vGRED4==2){document.getElementById("MRKMERIT4").value = 16;}
            else if(vGRED4==3){document.getElementById("MRKMERIT4").value = 14;}
            else if(vGRED4==4){document.getElementById("MRKMERIT4").value = 12;}
            else if(vGRED4==5){document.getElementById("MRKMERIT4").value = 10;}	
            else if(vGRED4==6){document.getElementById("MRKMERIT4").value = 8;}
            else if(vGRED4==7){document.getElementById("MRKMERIT4").value = 6;}	
            else if(vGRED4==8){document.getElementById("MRKMERIT4").value = 4;}
            else if(vGRED4==9){document.getElementById("MRKMERIT4").value = 2;}
            else {document.getElementById("MRKMERIT4").value = 0;}
            
            if(vGRED5==1){document.getElementById("MRKMERIT5").value = 18;}
            else if(vGRED5==2){document.getElementById("MRKMERIT5").value = 16;}
            else if(vGRED5==3){document.getElementById("MRKMERIT5").value = 14;}
            else if(vGRED5==4){document.getElementById("MRKMERIT5").value = 12;}
            else if(vGRED5==5){document.getElementById("MRKMERIT5").value = 10;}	
            else if(vGRED5==6){document.getElementById("MRKMERIT5").value = 8;}
            else if(vGRED5==7){document.getElementById("MRKMERIT5").value = 6;}	
            else if(vGRED5==8){document.getElementById("MRKMERIT5").value = 4;}
            else if(vGRED5==9){document.getElementById("MRKMERIT5").value = 2;}
            else {document.getElementById("MRKMERIT5").value = 0;}
            
            
            $jummeril1 = document.getElementById("MRKMERIT1").value;
            $jummeril2 = document.getElementById("MRKMERIT2").value;
            $jummeril3 = document.getElementById("MRKMERIT3").value;
            $jummeril4 = document.getElementById("MRKMERIT4").value;
            $jummeril5 = document.getElementById("MRKMERIT5").value;
            
            var total = parseFloat($jummeril1) + parseFloat($jummeril2) + parseFloat($jummeril3) + parseFloat($jummeril4) + parseFloat($jummeril5);
            document.getElementById("TOTAL1").value=total;  
        }
    </script>
    
    <script>
    
        function kira2()
            {
                var vGRED6 = document.getElementById("GRDGRED6").selectedIndex;
                var vGRED7 = document.getElementById("GRDGRED7").selectedIndex;
                var vGRED8 = document.getElementById("GRDGRED8").selectedIndex;
            
                if(vGRED6==1){document.getElementById("MRKMERIT6").value = 18;}
                else if(vGRED6==2){document.getElementById("MRKMERIT6").value = 16;}
                else if(vGRED6==3){document.getElementById("MRKMERIT6").value = 14;}
                else if(vGRED6==4){document.getElementById("MRKMERIT6").value = 12;}
                else if(vGRED6==5){document.getElementById("MRKMERIT6").value = 10;}	
                else if(vGRED6==6){document.getElementById("MRKMERIT6").value = 8;}
                else if(vGRED6==7){document.getElementById("MRKMERIT6").value = 6;}	
                else if(vGRED6==8){document.getElementById("MRKMERIT6").value = 4;}
                else if(vGRED6==9){document.getElementById("MRKMERIT6").value = 2;}
            
                if(vGRED7==1){document.getElementById("MRKMERIT7").value = 18;}
                else if(vGRED7==2){document.getElementById("MRKMERIT7").value = 16;}
                else if(vGRED7==3){document.getElementById("MRKMERIT7").value = 14;}
                else if(vGRED7==4){document.getElementById("MRKMERIT7").value = 12;}
                else if(vGRED7==5){document.getElementById("MRKMERIT7").value = 10;}	
                else if(vGRED7==6){document.getElementById("MRKMERIT7").value = 8;}
                else if(vGRED7==7){document.getElementById("MRKMERIT7").value = 6;}	
                else if(vGRED7==8){document.getElementById("MRKMERIT7").value = 4;}
                else if(vGRED7==9){document.getElementById("MRKMERIT7").value = 2;}
                
                if(vGRED8==1){document.getElementById("MRKMERIT8").value = 18;}
                else if(vGRED8==2){document.getElementById("MRKMERIT8").value = 16;}
                else if(vGRED8==3){document.getElementById("MRKMERIT8").value = 14;}
                else if(vGRED8==4){document.getElementById("MRKMERIT8").value = 12;}
                else if(vGRED8==5){document.getElementById("MRKMERIT8").value = 10;}	
                else if(vGRED8==6){document.getElementById("MRKMERIT8").value = 8;}
                else if(vGRED8==7){document.getElementById("MRKMERIT8").value = 6;}	
                else if(vGRED8==8){document.getElementById("MRKMERIT8").value = 4;}
                else if(vGRED8==9){document.getElementById("MRKMERIT8").value = 2;}
            
                $jummeril6 = document.getElementById("MRKMERIT6").value;
                $jummeril7 = document.getElementById("MRKMERIT7").value;
                $jummeril8 = document.getElementById("MRKMERIT8").value;
                
                var total2 = parseFloat($jummeril6) + parseFloat($jummeril7) + parseFloat($jummeril8);
                document.getElementById("TOTAL2").value=total2;  
            }
    </script>
    
    <script>
        function kiraSPM()
        {
            var JUMA = document.getElementById("TOTAL1").value;
            var JUMLAHALLA = JUMA/90*90;
            document.getElementById("MRKALL1").value=parseFloat(Number(JUMLAHALLA)).toFixed(2); 
            
            var JUMB = document.getElementById("TOTAL2").value;
            var JUMLAHALLB = JUMB/54*30;
            document.getElementById("MRKALL2").value=parseFloat(Number(JUMLAHALLB)).toFixed(2); 
            
            var JUMC = parseFloat(JUMLAHALLA) + parseFloat(JUMLAHALLB);
            var JUMLAHALLC = (JUMC/120)*90;
            document.getElementById("MRKALL3").value=parseFloat(Number(JUMLAHALLC)).toFixed(2);   
            
            var JUMD = document.getElementById("MRKALL4").value;
            var JUMLAHALLD = parseFloat(JUMLAHALLC) + parseFloat(JUMD);
            document.getElementById("TOTAL3").value=parseFloat(Number(JUMLAHALLD)).toFixed(2); 
        }
    </script>
    
    <script>
        $(document).ready(function()
            {	
                document.getElementById("MRKMERIT1").value = parseFloat(Number("0")).toFixed(0); 
                document.getElementById("MRKMERIT2").value = parseFloat(Number("0")).toFixed(0); 
                document.getElementById("MRKMERIT3").value = parseFloat(Number("0")).toFixed(0); 
                document.getElementById("MRKMERIT4").value = parseFloat(Number("0")).toFixed(0);
                document.getElementById("MRKMERIT5").value = parseFloat(Number("0")).toFixed(0); 
                document.getElementById("TOTAL1").value = parseFloat(Number("0")).toFixed(0);
                    
                document.getElementById("MRKMERIT6").value = parseFloat(Number("0")).toFixed(0); 
                document.getElementById("MRKMERIT7").value = parseFloat(Number("0")).toFixed(0); 
                document.getElementById("MRKMERIT8").value = parseFloat(Number("0")).toFixed(0); 
                document.getElementById("TOTAL2").value = parseFloat(Number("0")).toFixed(0); 
                        
                document.getElementById("MRKALL1").value = parseFloat(Number("0")).toFixed(2); 
                document.getElementById("MRKALL2").value = parseFloat(Number("0")).toFixed(2); 
                document.getElementById("MRKALL3").value = parseFloat(Number("0")).toFixed(2); 
                document.getElementById("MRKALL4").value = parseFloat(Number("0")).toFixed(2); 
                document.getElementById("TOTAL3").value = parseFloat(Number("0")).toFixed(2); 
            });
    </script>
    
    <script type="text/javascript">
        $(".maxmin").each(function () {

        var thisJ = $(this);
        var max = thisJ.attr("max") * 1;
        var min = thisJ.attr("min") * 1;
        var intOnly = String(thisJ.attr("intOnly")).toLowerCase() == "true";

        var test = function (str) {
            return str == "" || /* (!intOnly && str == ".") || */
                ($.isNumeric(str) && str * 1 <= max && str * 1 >= min &&
                (!intOnly || str.indexOf(".") == -1) && str.match(/^0\d/) == null);
                // commented out code would allow entries like ".7"
        };

        thisJ.keydown(function () {
            var str = thisJ.val();
            if (test(str)) thisJ.data("dwnval", str);
        });

        thisJ.keyup(function () {
            var str = thisJ.val();
            if (!test(str)) thisJ.val(thisJ.data("dwnval"));
        })
        });
    </script>
    
        