

@foreach ($j<PERSON><PERSON><PERSON><PERSON><PERSON> as $key => $jpengajian)

    @php
    $syaratam_am_spm  = DB::connection('emas')->table('syarat_am_pengajian')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->orderby('KOD_PENGAJIAN','ASC')->get();
    $syaratam_am  = DB::connection('emas')->table('syarat_am_pengajian_stpm')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->orderby('KOD_PENGAJIAN','ASC')->get();
    $syaratam_am_lain  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->orderby('KOD_PENGAJIAN','ASC')->get();
    $syaratam_am_muet  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->orderby('KOD_PENGAJIAN','ASC')->get();
    $syaratam_diploma  = DB::connection('emas')->table('syarat_am_diploma')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->orderby('KOD_PENGAJIAN','ASC')->get();

    $syaratam_am_lain_matrik  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->where('KATEGORI','matrik')->get();
    $syaratam_am_lain_stpm  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->where('KATEGORI','stpm')->get();
    $syaratam_am_lain_stam  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->where('KATEGORI','stam')->get();
    $syaratam_am_lain_diploma  = DB::connection('emas')->table('syarat_am_lain')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->where('KATEGORI','diploma')->get();
    @endphp



    <div class="pl-2 pb-3" style="line-height:35px; float:left; width: 90%; margin-top:-1px; margin-bottom: -15px;">

        @if($jpengajian->KOD_PENGAJIAN=='SM1' && ($PROGRAM->kategori_Pengajian=='P' || $PROGRAM->kategori_Pengajian=='L' || $PROGRAM->kategori_Pengajian=='U' || $PROGRAM->kategori_Pengajian=='N' || $PROGRAM->kategori_Pengajian=='K' || $PROGRAM->kategori_Pengajian=='J' || $PROGRAM->kategori_Pengajian=='M' || $PROGRAM->kategori_Pengajian=='V') ||
		$jpengajian->KOD_PENGAJIAN=='SM2' && ($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S') ||
		$jpengajian->KOD_PENGAJIAN=='SM3' && $PROGRAM->kategori_Pengajian=='T')
		@if ($loop->index)
            <div><u><b>SPM</b></u></div>
            <div>Memiliki <b>Sijil Pelajaran Malaysia (SPM)</b> dengan mendapat sekurang-kurangnya :-</div>
            <ol style="padding-left: 1.5em;">
                @foreach ($syaratam_am_spm as $syaratam_spm)
                    @if($syaratam_spm->KODSUBJEK!='F')
                        <li style="padding-left: .3em;">
                            Gred <b>{{$syaratam_spm->MINGRED}}</b> bagi matapelajaran
                            @foreach ($subjek as $spm_subjek)
                                @if($spm_subjek->kodsubjekspm==$syaratam_spm->KODSUBJEK)
                                    <b>{{$spm_subjek->ketsubjekspm}}@if($syaratam_spm->KERTAS_JULAI=='T' && $syaratam_spm->KODSUBJEK=='1103'). @endif
                                        @if($syaratam_spm->KERTAS_JULAI=='Y' && $syaratam_spm->KODSUBJEK=='1103') / BAHASA MELAYU (KERTAS JULAI). @endif
                                    </b>
                                    @if($syaratam_spm->KODSUBJEK=='1249') mulai tahun 2013. @endif
                                @endif
                            @endforeach
                        </li>
                    @endif

                    @php
                    if($syaratam_spm->JUMLAH_MIN_SUBJEK=='1') { $read='SATU'; }
                    elseif($syaratam_spm->JUMLAH_MIN_SUBJEK=='2') { $read='DUA'; }
                    elseif($syaratam_spm->JUMLAH_MIN_SUBJEK=='3') { $read='TIGA'; }
                    elseif($syaratam_spm->JUMLAH_MIN_SUBJEK=='4') { $read='EMPAT'; }
                    elseif($syaratam_spm->JUMLAH_MIN_SUBJEK=='5') { $read='LIMA'; }
                    @endphp

                    @if($syaratam_spm->KODSUBJEK=='F')
                        <li style="padding-left: .3em;"><b>{{$read}} ({{$syaratam_spm->JUMLAH_MIN_SUBJEK}})</b> Gred <b>{{$syaratam_spm->MINGRED}}</b> dalam mana-mana matapelajaran yang belum dikira.</li>
                    @endif
                @endforeach
            </ol>

            <div class="text-center"><b>DAN</b></div>
        @endif
        @endif

        <div>
            <u><b>
                @if($jpengajian->KOD_PENGAJIAN=='SM1' && ($PROGRAM->kategori_Pengajian=='P' || $PROGRAM->kategori_Pengajian=='L' || $PROGRAM->kategori_Pengajian=='U' || $PROGRAM->kategori_Pengajian=='N' || $PROGRAM->kategori_Pengajian=='K' || $PROGRAM->kategori_Pengajian=='J' || $PROGRAM->kategori_Pengajian=='M' || $PROGRAM->kategori_Pengajian=='V')) MATRIK / ASASI @endif
                @if($jpengajian->KOD_PENGAJIAN=='SM2' && ($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S')) STPM @endif
                @if($jpengajian->KOD_PENGAJIAN=='SM3' && $PROGRAM->kategori_Pengajian=='T') STAM @endif
            </b></u>
        </div>

        @if($jpengajian->KOD_PENGAJIAN=='SM1' && ($PROGRAM->kategori_Pengajian=='P' || $PROGRAM->kategori_Pengajian=='L' || $PROGRAM->kategori_Pengajian=='U' || $PROGRAM->kategori_Pengajian=='N' || $PROGRAM->kategori_Pengajian=='K' || $PROGRAM->kategori_Pengajian=='J' || $PROGRAM->kategori_Pengajian=='M' || $PROGRAM->kategori_Pengajian=='V'))
            @if ($loop->index)
            <div>Lulus Matrikulasi KPM / Asasi Sains UM / Asasi UKM / Asasi UiTM dengan mendapat sekurang-kurangnya :-</div>
            <ol style="padding-left: 1.5em;">
                @foreach ($syaratam_am_lain as $syaratam_lain)
                <li style="padding-left: .3em;">Purata Nilai Gred Kumulatif (PNGK)
                    <b>{{$syaratam_lain->PNGK}}</b>.
                </li>
                @endforeach

                @foreach ($syaratam_am as $syaratam)
                    @if($syaratam->KODSUBJEK=='F')
                        <li style="padding-left: .3em;"><b>{{$read}} ({{$syaratam->JUMLAH_MIN_SUBJEK}})</b> Gred <b>{{$syaratam->MINGRED}}</b> dalam mana-mana matapelajaran yang belum dikira.</li>
                    @endif
                @endforeach
            </ol>
            @endif
        @endif


        @if($jpengajian->KOD_PENGAJIAN=='SM2' && ($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S'))
        @if ($loop->index)
        <div>Lulus Peperiksaan <b>Sijil Tinggi Persekolahan Malaysia (STPM)</b> dengan mendapat sekurang-kurangnya :-</div>
        <ol style="padding-left: 1.5em;">
            @foreach ($syaratam_am_lain as $syaratam_lain)
            <li style="padding-left: .3em;">Purata Nilai Gred Kumulatif (PNGK)
                <b>{{$syaratam_lain->PNGK}}</b>.
            </li>
            @endforeach

            @foreach ($syaratam_am as $syaratam)
                @if($syaratam->KODSUBJEK!='F')
                    <li style="padding-left: .3em;">
                        Gred <b>{{$syaratam->MINGRED}}</b> bagi matapelajaran
                        @foreach ($subjek_stpm as $stpm_subjek)
                            @if($stpm_subjek->kodsubjekstpm==$syaratam->KODSUBJEK)
                                <b>{{$stpm_subjek->ketsubjekstpm}}</b>.
                            @endif
                        @endforeach
                    </li>
                @endif
                @php
                if($syaratam->JUMLAH_MIN_SUBJEK=='1') { $read='SATU'; }
                elseif($syaratam->JUMLAH_MIN_SUBJEK=='2') { $read='DUA'; }
                elseif($syaratam->JUMLAH_MIN_SUBJEK=='3') { $read='TIGA'; }
                elseif($syaratam->JUMLAH_MIN_SUBJEK=='4') { $read='EMPAT'; }
                elseif($syaratam->JUMLAH_MIN_SUBJEK=='5') { $read='LIMA'; }
                @endphp

                @if($syaratam->KODSUBJEK=='F')
                    <li style="padding-left: .3em;"><b>{{$read}} ({{$syaratam->JUMLAH_MIN_SUBJEK}})</b> Gred <b>{{$syaratam->MINGRED}}</b> dalam mana-mana matapelajaran yang belum dikira.</li>
                @endif
            @endforeach
        </ol>
        @endif
    @endif

    @if($jpengajian->KOD_PENGAJIAN=='SM3' && $PROGRAM->kategori_Pengajian=='T')
    @if ($loop->index)
        <div>Mendapat sekurang-kurangnya :-</div>
        <ol style="padding-left: 1.5em;">
            @foreach ($syaratam_am_lain as $syaratam_lain)
            <li style="padding-left: .3em;">Pangkat

                @foreach ($codeset_stam as $codesetstam)
                    @if($codesetstam->kodthpstam==$syaratam_lain->TAHAP_STAM)
                        <b>{{$codesetstam->ketthpstam}}</b> dalam Peperiksaan Sijil Tinggi Agama Malaysia (STAM).
                    @endif
                @endforeach

            </li>
            @endforeach

            @foreach ($syaratam_am as $syaratam)
                @if($syaratam->KODSUBJEK=='F')
                <li style="padding-left: .3em;"><b>{{$read}} ({{$syaratam->JUMLAH_MIN_SUBJEK}})</b> Gred <b>{{$syaratam->MINGRED}}</b> dalam mana-mana matapelajaran yang belum dikira.</li>
                @endif
            @endforeach
        </ol>
    @endif
    @endif

    @if($jpengajian->KOD_PENGAJIAN=='SM1' && ($PROGRAM->kategori_Pengajian=='P' || $PROGRAM->kategori_Pengajian=='L' || $PROGRAM->kategori_Pengajian=='U' || $PROGRAM->kategori_Pengajian=='N' || $PROGRAM->kategori_Pengajian=='K' || $PROGRAM->kategori_Pengajian=='J' || $PROGRAM->kategori_Pengajian=='M' || $PROGRAM->kategori_Pengajian=='V') ||
    ($jpengajian->KOD_PENGAJIAN=='SM2' && ($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S')) ||
    ($jpengajian->KOD_PENGAJIAN=='SM3' && $PROGRAM->kategori_Pengajian=='T') ||
    ($jpengajian->KOD_PENGAJIAN=='SM4' && $PROGRAM->kategori_Pengajian=='G') ||
    ($jpengajian->KOD_PENGAJIAN=='SM5' && $PROGRAM->kategori_Pengajian=='E') ||
    ($jpengajian->KOD_PENGAJIAN=='SM6' && $PROGRAM->kategori_Pengajian=='E'))

    <div class="text-center"><b>DAN</b></div>
        <div> <u><b>MUET</b></u></div>
        <div>Mendapat sekurang-kurangnya
            @foreach ($syaratam_am_lain as $syaratam_lain)
                @foreach ($codeset_muet2 as $muet2)
                    @if($syaratam_lain->MUET1_BAND==$muet2->kodthpmuet)
                        <b style="text-transform: capitalize;">{{$muet2->ketthpmuet}}</b> dalam <b>Malaysian University English Test (MUET)</b> untuk peperiksaan yang bermula Sesi 1 tahun {{$syaratam_lain->TAHUN1_BAND}} <b>ATAU</b>
                    @endif
                @endforeach
                <b>BAND {{$syaratam_lain->MUET2_BAND}}</b> untuk peperiksaan sehingga tahun {{$syaratam_lain->TAHUN2_BAND}} <b>mengikut tempoh sah laku pada tarikh permohonan</b>.
            @endforeach
        </div>
    @endif


    </div>


@endforeach

