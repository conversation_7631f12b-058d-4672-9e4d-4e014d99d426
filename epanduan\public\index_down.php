<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistem Diselenggara</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap');

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(to bottom, #388BC7, #1E5275);
            color: white;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            animation: fadeIn 1.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(12px);
            padding: 2.5rem;
            border-radius: 14px;
            box-shadow: 0px 10px 25px rgba(0, 0, 0, 0.3);
            max-width: 520px;
        }

        .button-soft {
            background: #ffffff22;
            border: 1px solid rgba(255, 255, 255, 0.5);
            padding: 14px 28px;
            border-radius: 10px;
            transition: all 0.3s ease-in-out;
            font-weight: 600;
        }

        .button-soft:hover {
            background: rgba(255, 255, 255, 0.4);
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="glass-card">
        <!-- Official Logo -->
        <img src="https://online.mohe.gov.my/UPUOnlinev2/imgs/kpt.png" alt="KPT Logo" class="w-32 mx-auto mb-4 shadow-lg">

        <h1 class="text-3xl font-semibold mb-2">Akses Log Masuk Ditutup</h1>
        <p class="text-lg opacity-90 mb-4">Fasa 1 Permohonan Telah Ditutup Pada Jam 5.00 Petang.</p>

        <!-- Countdown Timer -->
        <div x-data="{ timeLeft: 1800 }" x-init="setInterval(() => timeLeft--, 1000)" class="text-xl font-bold mt-4 opacity-90">
            Baki Masa Sebelum Sistem Tutup Sepenuhnya:
            <span x-text="Math.floor(timeLeft / 60).toString().padStart(2, '0')"></span>:
            <span x-text="(timeLeft % 60).toString().padStart(2, '0')"></span>
        </div>

        <!-- Refresh Button -->
        <button onclick="location.reload()" class="button-soft mt-6">
            🔄 Muat Semula Halaman
        </button>

        <!-- Support Banner -->
        <div class="mt-4">
            <img src="{{ asset('images/banner.png') }}" alt="Support Banner" class="w-72 mx-auto rounded-lg shadow-lg">
        </div>
    </div>
</body>
</html>
