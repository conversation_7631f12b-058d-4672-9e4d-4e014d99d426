<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8" />
	<meta name="google-site-verification" content="knbeuhwyVSjnicym-QFZzp8uUS5kxLEj-FnUzAt3mcM" />
    <title>e-Panduan UPUOnline</title>
    <link rel="icon" href="{{ asset('/assets/images/_icons/_logos/favicon.png') }}">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <!-- Bootstrap -->
    <link href="{{ asset('/assets/css/bootstrap.min.css') }}" rel="stylesheet" type="text/css" />
    <!-- Icons -->
    <link href="{{ asset('/assets/css/materialdesignicons.min.css') }}" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="{{ asset('/assets/css/line.css') }}" />
    <!-- Magnific -->
    <link href="{{ asset('/assets/css/magnific-popup.css') }}" rel="stylesheet" type="text/css" />
    <!-- Slider -->
    <link rel="stylesheet" href="{{ asset('/assets/css/owl.carousel.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('/assets/css/owl.theme.default.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('/assets/css/slick.css') }}" />
    <link rel="stylesheet" href="{{ asset('/assets/css/slick-theme.css') }}" />
    <!-- Main Css -->
    <link href="{{ asset('/assets/css/style.css') }}" rel="stylesheet" type="text/css" id="theme-opt" />
    <link href="{{ asset('/assets/css/colors/default.css') }}" rel="stylesheet" id="color-opt" />
    <link rel="stylesheet" href="{{ asset('/assets/css/fonts.css') }}" />

       <!-- SELECT OPTION SEARCHING FORM CSS -->
   <link rel="stylesheet" type="text/css" href="{{ asset('css/select2.min.css') }}" >

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    @stack('styles')

</head>

<body>

    <style>
        @media (max-width: 575px){
            .d-sm1-block {
                display: block !important;
            }
        }

    .col_badge {
        width: 170px;
        border: 2px solid deeppink;
        border-color: deeppink !important;
        border-radius: 20px;
        padding-left: 10px;
        padding-right: 10px;
        padding-top: 4px;
        padding-bottom: 3px;
        font-weight: normal;
        text-align: left;
        /* font-size: 14px; */
    }

    .col_badge:hover  {
        box-shadow: none;
        border-color: none;
        background-color: deeppink !important;
    }

     .badge-deeppink  {
        background-color: deeppink !important;
        color: #ffffff !important;
        font-size: 13px !important;
    }

    .tooltip-inner {
        color: #000 !important;
        background-color: #F8FCC4 !important;
        max-width: 400px;
        text-align: left;
    }

    @media (min-width: 1200px) {
        .modal-llgg {
            max-width: 900px;
        }
    }
    </style>

	<!-- Google tag (gtag.js) -->
	<script async src="https://www.googletagmanager.com/gtag/js?id=G-WTPJ19WGDH"></script>
	<script>
	  window.dataLayer = window.dataLayer || [];
	  function gtag(){dataLayer.push(arguments);}
	  gtag('js', new Date());

	  gtag('config', 'G-WTPJ19WGDH');
	</script>



    <header id="topnav" class="defaultscroll sticky">
        <div class="row">
        <div class="container" style="max-width:70%">
            <div>
                <a class="logo" href="{{ url('/') }}">
                    <img src="{{ asset('/assets/images/_icons/_logos/LogoKPT-JPT.svg') }}" height="52" alt="" />
                </a>
            </div>
            <div class="menu-extras">
                <div class="menu-item">
                    <a class="navbar-toggle">
                        <div class="lines">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                    </a>
                </div>
            </div>

            <div id="navigation">
                <!-- Navigation Menu-->
                <ul class="navigation-menu nav-right">
                    <li><a href="{{ url('/') }}">Laman Utama</a></li>
                    <li><a href="{{ url('senaraiAgensi') }}">Senarai IPTA & ILKA</a></li>
                    <li><a href="{{ url('carianNamaProgram') }}">Carian Program</a></li>
                    <li class="has-submenu">
                        <a>Syarat Kemasukan</a><span class="menu-arrow"></span>
                        <ul class="submenu">
                            <li><a href="<?= url('kategoriCalon?jenprog=spm') ?>" style="font-size: 13px">Lepasan
                                    SPM</a></li>
                            <li><a href="<?= url('kategoriCalon?jenprog=stpm') ?>" style="font-size: 13px">Lepasan STPM
                                    /
                                    Setaraf</a></li>
                        </ul>
                    </li>
                    <li class="has-submenu d-none d-lg-block d-xl-block" style="margin-left:25px">
                        <a>Kalkulator Merit</a><span class="menu-arrow"></span>
                        <ul class="submenu">
                            <li><a href="{{ url('kalkulatorMerit/spm') }}" style="font-size: 13px">Lepasan SPM</a></li>
                            <li><a href="{{ url('kalkulatorMerit/stpm') }}" style="font-size: 13px">Lepasan STPM / Setaraf</a></li>
                        </ul>
                    </li>
                    <li class="has-submenu d-none d-sm-block d-sm1-block d-md-block d-lg-none d-xl-none">
                        <a>Kalkulator Merit</a><span class="menu-arrow"></span>
                        <ul class="submenu">
                            <li><a href="{{ url('kalkulatorMerit/spm') }}" style="font-size: 13px">Lepasan SPM</a></li>
                            <li><a href="{{ url('kalkulatorMerit/stpm') }}" style="font-size: 13px">Lepasan STPM / Setaraf</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    </header>

    @yield('content')

    <!-- Footer Start -->
    <footer class="footer">
        <div class="container">
            <div class="row" style="justify-content: center">
                <div class="col-lg-4 col-12 mb-0 mb-md-4 pb-0 pb-md-2">
                    <a href="{{ url('/') }}" class="logo-footer">
                        <img src="{{ asset('/assets/images/_icons/_logos/LogoKPT-JPT-2.svg') }}" width="300"
                            alt="">
                    </a>
                    <p class="mt-4">
                        Bahagian Kemasukan Pelajar IPTA<br>Jabatan Pendidikan Tinggi,<br>Kementerian Pengajian
                        Tinggi<br>Aras 4, No. 2 Menara 2, Jalan P5/6<br>Presint 5 Pusat Pentadbiran
                        Kerajaan<br>Persekutuan 62200<br>Wilayah Persekutuan Putrajaya
                    </p>
                </div>

                <div class="col-lg-3 col-12 mt-4 mt-sm-0 pt-2 pt-sm-0">
                    <ul class="list-unstyled footer-list mt-4 sidebar-menu">
                        <li><a href="{{ url('/') }}" class="text-foot"><i class="mdi mdi-chevron-right mr-1"></i>
                                Laman
                                Utama</a></li>
                        <li><a href="{{ url('senaraiAgensi') }}" class="text-foot"><i
                                    class="mdi mdi-chevron-right mr-1"></i>
                                IPTA & ILKA</a></li>
                        <li><a href="{{ url('carianNamaProgram') }}" class="text-foot"><i
                                    class="mdi mdi-chevron-right mr-1"></i>
                                Carian Program</a></li>
                        <li class="have-children">
                            <a><i class="mdi mdi-chevron-right mr-1"></i>Syarat Kemasukan</a>
                            <ul class="list-unstyled">
                                <li>
                                    <a href="<?= url('kategoriCalon?jenprog=spm') ?>"
                                        style="font-size: 13px; color: #adb5bd"
                                        onclick="event.preventDefault(); window.location.href='<?= url('kategoriCalon?jenprog=spm') ?>'">
                                        Lepasan SPM
                                    </a>
                                </li>
                                <li><a href="<?= url('kategoriCalon?jenprog=stpm') ?>"
                                        onclick="event.preventDefault(); window.location.href='<?= url('kategoriCalon?jenprog=stpm') ?>'"
                                        style="font-size: 13px; color: #adb5bd">Lepasan
                                        STPM
                                        /
                                        Setaraf</a></li>
                            </ul>
                        </li>

                        <li class="have-children">
                            <a><i class="mdi mdi-chevron-right mr-1"></i>Kalkulator Merit</a>
                            <ul class="list-unstyled">
                                <li>
                                    <a href="{{ url('kalkulatorMerit/spm') }}"
                                        style="font-size: 13px; color: #adb5bd"
                                        onclick="event.preventDefault(); window.location.href='<?= url('kalkulatorMerit/spm') ?>'">
                                        Lepasan SPM
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ url('kalkulatorMerit/stpm') }}"
                                        onclick="event.preventDefault(); window.location.href='<?= url('kalkulatorMerit/stpm') ?>'"
                                        style="font-size: 13px; color: #adb5bd">Lepasan STPM / Setaraf
                                    </a>
                                </li>
                            </ul>
                        </li>




                    </ul>
                </div>
            </div>
        </div>
    </footer>


<div class="chat-icon">
    <a href="#" onclick="window.open('https://upuchat.org/UPU_DaftarMasuk', 'UPUChat', 'width=800,height=600,top=100,left=100'); return false;">
        <div class="modern-image-wrapper">
            <!-- Using a placeholder image URL as fallback -->
            <img src="{{ asset('/assets/images/upuchat.png') }}" alt="Chat" onerror="this.src='https://via.placeholder.com/80'">
            <span class="notification-badge">1</span>
        </div>
    </a>
</div>

<style>
    .chat-icon {
        display: inline-block; /* Ensures proper sizing */
    }

    .modern-image-wrapper {
        position: relative;
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: white;
        padding: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        transition: all 0.3s ease;
        display: flex; /* Helps with image alignment */
        align-items: center;
        justify-content: center;
    }

    .modern-image-wrapper img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        /* No color filters - keeping original colors */
    }

    .notification-badge {
        position: absolute;
        top: 0;
        right: 0;
        background: #ff4757;
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
        animation: ping 1.5s infinite;
    }

    @keyframes ping {
        0% { transform: scale(1); }
        50% { transform: scale(1.2); }
        100% { transform: scale(1); }
    }

    .modern-image-wrapper:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    }
</style>



    <a href="#" class="btn btn-icon btn-primary back-to-top"><i data-feather="arrow-up" class="icons"></i></a>
    <!-- Back to top -->

    <!-- javascript -->
    <script src="{{ asset('/assets/js/jquery-3.5.1.min.js') }}"></script>
    <script src="{{ asset('/assets/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ asset('/assets/js/jquery.easing.min.js') }}"></script>
    <script src="{{ asset('/assets/js/scrollspy.min.js') }}"></script>
    <script src="{{ asset('/assets/js/sidebar.js') }}"></script>
    <script src="{{ asset('/assets/js/font-awesome.js') }}"></script>
    <script src="{{ asset('/assets/js/paginator.js') }}"></script>

    <!-- Magnific -->
    <script src="{{ asset('/assets/js/jquery.magnific-popup.min.js') }}"></script>
    <script src="{{ asset('/assets/js/isotope.pkgd.min.js') }}"></script>
    <script src="{{ asset('/assets/js/portfolio.init.js') }}"></script>

    <!-- Icons -->
    <script src="{{ asset('/assets/js/feather.min.js') }}"></script>

    <!-- Main Js -->
    <script src="{{ asset('/assets/js/app.js') }}"></script>
    <script src="{{ asset('/assets/js/modal.js') }}"></script>

    <script type="text/javascript" src="{{ asset('js/select2.min.js') }}"></script>

    @if(Request::path()=='kalkulatorMerit/spm')
        @include('kalkulatorMerit.calc_spm_js')
    @endif

    @if(Request::path()=='kalkulatorMerit/stpm')

        <span id="stpm_page">
            @include('kalkulatorMerit.calc_stpm_js')
        </span>

        <span id="stam_page">
            @include('kalkulatorMerit.calc_stam_js')
        </span>

        <span id="diploma_page">
            @include('kalkulatorMerit.calc_diploma_js')
        </span>

    @endif

    <script>
        $(document).ready(function(){
            $("#stpm_page").show();
            $("#diploma_page").hide();
            $("#stam_page").hide();
        });
    </script>

    <script>
        $(document).ready(function(){
            $(".progControlSelect2").select2()
        });

    function kategori()
    {
        var katag = document.getElementById("KATEGORI");
        var katag = katag.value;

        if (katag=="stpm")
        {
            $("#stpm_page").show();
            $("#diploma_page").hide();
            $("#stam_page").hide();

            document.getElementById("MRKPNGKSTPM").value = parseFloat(Number("0.0")).toFixed(2);
            document.getElementById("MRKMERITSTPM1").value = parseFloat(Number("0")).toFixed(2);
            $('#MRKKOKOSTPM').val('10.00').select2();
            document.getElementById("TOTALSTPM1").value = parseFloat(Number("0.0")).toFixed(2);


        }
        else if (katag=="stam")
        {
            $("#stpm_page").hide();
            $("#diploma_page").hide();
            $("#stam_page").show();

            document.getElementById("MRKPNGSTAM").selectedIndex='';
            document.getElementById("MRKMERITSTAM1").value = parseFloat(Number("0")).toFixed(2);
            $('#MRKKOKOSTAM').val('10.00').select2();
            document.getElementById("TOTALSTAM1").value = parseFloat(Number("0.0")).toFixed(2);

        }
        else if (katag=="diploma")
        {
            $("#stpm_page").hide();
            $("#diploma_page").show();
            $("#stam_page").hide();

            document.getElementById("MRKPNGKDIPLOMA").value = parseFloat(Number("0.0")).toFixed(2);
            document.getElementById("TOTALDIPLOMA").value = parseFloat(Number("0")).toFixed(2);

        }
        else
        {
            $("#stpm_page").show();
            $("#diploma_page").hide();
            $("#stam_page").hide();

            document.getElementById("MRKPNGKSTPM").value = parseFloat(Number("0.0")).toFixed(2);
            document.getElementById("MRKMERITSTPM1").value = parseFloat(Number("0")).toFixed(2);
            $('#MRKKOKOSTPM').val('10.00').select2();
            document.getElementById("TOTALSTPM1").value = parseFloat(Number("0.0")).toFixed(2);

        }
    }
    </script>

    <script>
        $(document).ready(function() {

            $('#carianKategori').on('change',function()
            {

                if($(this).data('options') == undefined)
                {
                    $(this).data('options', $('#kategoriProgram option').clone());
                }

                if($(this).data('options1') == undefined)
                {
                    $(this).data('options1', $('#carianIPTA option').clone());
                }

                    var katag1 = $(this).val();
                    // alert(katag1);
                    if (katag1=='spm')
                    {
                        var options1 = $(this).data('options').filter('[data-value=""]');
                        var options2 = $(this).data('options').filter('[data-value=' + katag1 + ']');
                        $('#kategoriProgram').html([options1,options2]).show();

                        var options3 = $(this).data('options1').filter('[data-value=""]');
                        var options4 = $(this).data('options1').filter('[data-value=' + katag1 + ']');
                        $('#carianIPTA').html([options3,options4]).show();

                        $('#carianKategori').on('change', function()
                        {
                            document.getElementById("kategoriProgram").value ="";
                            document.getElementById("carianIPTA").value ="";
                        });

                    }
                    else if (katag1=='stpm')
                    {
                        var options1 = $(this).data('options').filter('[data-value=""]');
                        var options2 = $(this).data('options').filter('[data-value=' + katag1 + ']');
                        $('#kategoriProgram').html([options1,options2]).show();

                        var options3 = $(this).data('options1').filter('[data-value2=""]');
                        var options4 = $(this).data('options1').filter('[data-value2=' + katag1 + ']');
                        $('#carianIPTA').html([options3,options4]).show();

                        $('#carianKategori').on('change', function()
                        {
                            document.getElementById("kategoriProgram").value ="";
                            document.getElementById("carianIPTA").value ="";
                        });
                    }
                    else
                    {
                        var options1 = $(this).data('options').filter('[data-value=""]');
                        document.getElementById("kategoriProgram").value ="";
                        $('#kategoriProgram').html(options1).show();

                        var options2 = $(this).data('options1').filter('[data-value=""]');
                        document.getElementById("carianIPTA").value ="";
                        $('#carianIPTA').html(options2).show();

                        $('#carianKategori').on('change', function()
                        {
                            document.getElementById("kategoriProgram").value ="";
                            document.getElementById("carianIPTA").value ="";
                        });
                    }
            });
        });
    </script>

    <script>
        $(document).ready(function() {

            $('#carianKategori').trigger('change',function()
            {

                if($(this).data('options') == undefined)
                {
                    $(this).data('options', $('#kategoriProgram option').clone());
                }

                if($(this).data('options1') == undefined)
                {
                    $(this).data('options1', $('#carianIPTA option').clone());
                }

                var katag1 = $(this).val();

                if (katag1=='spm')
                {
                    var options1 = $(this).data('options').filter('[data-value=""]');
                    var options2 = $(this).data('options').filter('[data-value=' + katag1 + ']');
                    $('#kategoriProgram').html([options1,options2]).show();

                    var options3 = $(this).data('options1').filter('[data-value=""]');
                    var options4 = $(this).data('options1').filter('[data-value=' + katag1 + ']');
                    $('#carianIPTA').html([options3,options4]).show();

                    $('#carianKategori').on('change', function()
                    {
                        document.getElementById("kategoriProgram").value ="";
                        document.getElementById("carianIPTA").value ="";
                    });
                }
                else if (katag1=='stpm')
                {
                    var options1 = $(this).data('options').filter('[data-value=""]');
                    var options2 = $(this).data('options').filter('[data-value=' + katag1 + ']');
                    $('#kategoriProgram').html([options1,options2]).show();

                    var options3 = $(this).data('options1').filter('[data-value2=""]');
                    var options4 = $(this).data('options1').filter('[data-value2=' + katag1 + ']');
                    $('#carianIPTA').html([options3,options4]).show();

                    $('#carianKategori').on('change', function()
                    {
                        document.getElementById("kategoriProgram").value ="";
                        document.getElementById("carianIPTA").value ="";
                    });
                }
                else
                {
                    var options1 = $(this).data('options').filter('[data-value=""]');
                    document.getElementById("kategoriProgram").value ="";
                    $('#kategoriProgram').html(options1).show();

                    var options2 = $(this).data('options1').filter('[data-value=""]');
                    document.getElementById("carianIPTA").value ="";
                    $('#carianIPTA').html(options2).show();

                    $('#carianKategori').on('change', function()
                    {
                        document.getElementById("kategoriProgram").value ="";
                        document.getElementById("carianIPTA").value ="";
                    });
                }
            });
        });
    </script>

	<script>
		$(document).ready(function(){
			$('[data-toggle="tooltip"]').tooltip();
		});
	</script>

    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

</body>

</html>
