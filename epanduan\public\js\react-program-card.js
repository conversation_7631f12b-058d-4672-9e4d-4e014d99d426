// React-Bootstrap Program Card Component
const { Card, Row, Col, Badge, ButtonGroup, Button, OverlayTrigger, Tooltip } = ReactBootstrap;

class ProgramCard extends React.Component {
    constructor(props) {
        super(props);
        this.handleCardClick = this.handleCardClick.bind(this);
        this.handleButtonClick = this.handleButtonClick.bind(this);
        this.renderTooltip = this.renderTooltip.bind(this);
    }

    handleCardClick(e) {
        // Prevent card click when clicking on buttons
        if (e.target.closest('.btn') || e.target.closest('button')) {
            return;
        }
        
        // Open program info modal
        const modal = document.querySelector('#maklumatProgram__' + this.props.programData.kodProgram);
        if (modal && typeof bootstrap !== 'undefined') {
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
        }
    }

    handleButtonClick(e, modalTarget) {
        e.stopPropagation();
        const modal = document.querySelector(modalTarget);
        if (modal && typeof bootstrap !== 'undefined') {
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();
        }
    }

    renderTooltip(text) {
        return React.createElement(Tooltip, { 
            id: `tooltip-${this.props.programData.kodProgram}` 
        }, text);
    }

    render() {
        const { programData } = this.props;
        
        return React.createElement(Card, {
            className: "shadow-sm border-0 h-100",
            style: { 
                borderRadius: '12px',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
            },
            onClick: this.handleCardClick,
            onMouseEnter: (e) => {
                e.currentTarget.style.transform = 'translateY(-4px)';
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
            },
            onMouseLeave: (e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '';
            }
        }, 
            React.createElement(Card.Body, { className: "p-3" },
                // Program Header
                React.createElement(Row, { className: "align-items-center mb-3" },
                    React.createElement(Col, { xs: "auto" },
                        React.createElement('div', {
                            style: {
                                width: '60px',
                                height: '60px',
                                background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                                borderRadius: '12px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                            }
                        },
                            React.createElement('i', { 
                                className: "fas fa-graduation-cap text-primary", 
                                style: { fontSize: '24px' } 
                            })
                        )
                    ),
                    React.createElement(Col, null,
                        React.createElement(Card.Title, {
                            className: "mb-2",
                            style: {
                                fontSize: '1.25rem',
                                fontWeight: '700',
                                color: '#2c3e50',
                                lineHeight: '1.3'
                            }
                        },
                            programData.namaProgram,
                            programData.hasInterview && React.createElement(Badge, {
                                bg: "danger",
                                className: "ms-2"
                            },
                                React.createElement('i', { className: "fas fa-user-tie me-1" }),
                                "Interview"
                            )
                        ),
                        
                        React.createElement(Card.Subtitle, {
                            className: "mb-3 text-muted",
                            style: {
                                fontSize: '0.9rem',
                                fontWeight: '500'
                            }
                        },
                            React.createElement('i', { className: "fas fa-university me-2" }),
                            programData.namaIPTA
                        ),

                        // Program Badges
                        React.createElement('div', { className: "mb-3" },
                            programData.jenProg === 'spm' && [
                                programData.programFeeder && React.createElement(OverlayTrigger, {
                                    key: 'feeder',
                                    placement: "top",
                                    overlay: this.renderTooltip("Program pembekal kepada program Ijazah Sarjana Muda di Universiti Awam berkenaan sahaja.")
                                },
                                    React.createElement(Badge, {
                                        bg: "warning",
                                        className: "me-2 mb-1",
                                        style: { fontSize: '0.75rem' }
                                    },
                                        React.createElement('i', { className: "fas fa-seedling me-1" }),
                                        "Program Perintis"
                                    )
                                ),
                                programData.programSTEM && React.createElement(Badge, {
                                    key: 'stem',
                                    bg: "primary",
                                    className: "me-2 mb-1",
                                    style: { fontSize: '0.75rem' }
                                },
                                    React.createElement('i', { className: "fas fa-atom me-1" }),
                                    "STEM"
                                ),
                                programData.programTVET && React.createElement(OverlayTrigger, {
                                    key: 'tvet',
                                    placement: "top",
                                    overlay: this.renderTooltip("Program pengajian yang berfokus kepada pembangunan bakat holistik.")
                                },
                                    React.createElement(Badge, {
                                        bg: "info",
                                        className: "me-2 mb-1",
                                        style: { fontSize: '0.75rem' }
                                    },
                                        React.createElement('i', { className: "fas fa-tools me-1" }),
                                        "TVET"
                                    )
                                )
                            ].filter(Boolean),
                            
                            programData.jenProg === 'stpm' && [
                                programData.programKompetitif && React.createElement(Badge, {
                                    key: 'kompetitif',
                                    bg: "danger",
                                    className: "me-2 mb-1",
                                    style: { fontSize: '0.75rem' }
                                },
                                    React.createElement('i', { className: "fas fa-trophy me-1" }),
                                    "Kompetitif"
                                ),
                                programData.programBTECH && React.createElement(Badge, {
                                    key: 'btech',
                                    bg: "secondary",
                                    className: "me-2 mb-1",
                                    style: { fontSize: '0.75rem' }
                                },
                                    React.createElement('i', { className: "fas fa-cogs me-1" }),
                                    "BTECH"
                                ),
                                programData.programTVET && React.createElement(OverlayTrigger, {
                                    key: 'tvet-stpm',
                                    placement: "top",
                                    overlay: this.renderTooltip("Program pengajian yang berfokus kepada pembangunan bakat holistik.")
                                },
                                    React.createElement(Badge, {
                                        bg: "info",
                                        className: "me-2 mb-1",
                                        style: { fontSize: '0.75rem' }
                                    },
                                        React.createElement('i', { className: "fas fa-tools me-1" }),
                                        "TVET"
                                    )
                                )
                            ].filter(Boolean)
                        )
                    )
                ),

                // 3-Column Data Layout
                this.renderDataColumns(programData),

                // Action Buttons
                this.renderActionButtons(programData)
            )
        );
    }

    renderDataColumns(programData) {
        const dataCardStyle = {
            background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
            border: '1px solid #e9ecef',
            borderRadius: '8px',
            padding: '1rem',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            transition: 'all 0.3s ease'
        };

        const iconStyle = (bgColor) => ({
            width: '40px',
            height: '40px',
            background: bgColor,
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: '0.75rem'
        });

        const labelStyle = {
            fontSize: '0.7rem',
            fontWeight: '700',
            color: '#6c757d',
            textTransform: 'uppercase',
            letterSpacing: '1px',
            marginBottom: '0.25rem'
        };

        const valueStyle = {
            fontSize: '1rem',
            fontWeight: '700',
            color: '#2c3e50'
        };

        return React.createElement(Row, { className: "g-3 mb-4" },
            // Kod Program
            React.createElement(Col, { md: 4 },
                React.createElement('div', {
                    style: dataCardStyle,
                    onMouseEnter: (e) => {
                        e.currentTarget.style.transform = 'translateY(-2px)';
                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
                    },
                    onMouseLeave: (e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = '';
                    }
                },
                    React.createElement('div', {
                        style: iconStyle('linear-gradient(135deg, #007bff 0%, #0056b3 100%)')
                    },
                        React.createElement('i', { className: "fas fa-tag text-white" })
                    ),
                    React.createElement('div', null,
                        React.createElement('div', { style: labelStyle }, "KOD PROGRAM"),
                        React.createElement('div', { style: valueStyle }, programData.kodProgram)
                    )
                )
            ),
            
            // Tahun
            React.createElement(Col, { md: 4 },
                React.createElement('div', {
                    style: dataCardStyle,
                    onMouseEnter: (e) => {
                        e.currentTarget.style.transform = 'translateY(-2px)';
                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
                    },
                    onMouseLeave: (e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = '';
                    }
                },
                    React.createElement('div', {
                        style: iconStyle('linear-gradient(135deg, #28a745 0%, #1e7e34 100%)')
                    },
                        React.createElement('i', { className: "far fa-calendar-alt text-white" })
                    ),
                    React.createElement('div', null,
                        React.createElement('div', { style: labelStyle }, "TAHUN"),
                        React.createElement('div', { style: valueStyle }, programData.tahun)
                    )
                )
            ),
            
            // Merit
            React.createElement(Col, { md: 4 },
                React.createElement('div', {
                    style: dataCardStyle,
                    onMouseEnter: (e) => {
                        e.currentTarget.style.transform = 'translateY(-2px)';
                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
                    },
                    onMouseLeave: (e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = '';
                    }
                },
                    React.createElement('div', {
                        style: iconStyle('linear-gradient(135deg, #17a2b8 0%, #117a8b 100%)')
                    },
                        React.createElement('i', { className: "fas fa-chart-line text-white" })
                    ),
                    React.createElement('div', null,
                        React.createElement('div', { style: labelStyle }, "MERIT"),
                        React.createElement('div', { style: valueStyle },
                            programData.merit,
                            programData.merit !== 'Tiada' && React.createElement(OverlayTrigger, {
                                placement: "top",
                                overlay: this.renderTooltip("Tiada jaminan mendapat tawaran berdasarkan markah merit semata-mata.")
                            },
                                React.createElement('i', { 
                                    className: "fas fa-info-circle text-primary ms-1", 
                                    style: { cursor: 'pointer' } 
                                })
                            )
                        )
                    )
                )
            )
        );
    }

    renderActionButtons(programData) {
        const buttonStyle = {
            borderRadius: '6px',
            fontWeight: '600',
            fontSize: '0.8rem',
            padding: '0.5rem 1rem',
            textTransform: 'uppercase',
            letterSpacing: '0.5px',
            minWidth: '90px'
        };

        return React.createElement('div', { className: "d-flex flex-wrap gap-2 justify-content-center" },
            React.createElement(ButtonGroup, { size: "sm", className: "flex-wrap" },
                React.createElement(Button, {
                    variant: "outline-primary",
                    style: buttonStyle,
                    onClick: (e) => {
                        e.stopPropagation();
                        this.handleButtonClick(e, '#subjek_' + programData.kodProgram);
                    }
                },
                    React.createElement('i', { className: "fas fa-book me-1" }),
                    "SYARAT"
                ),
                
                React.createElement(Button, {
                    variant: "outline-success",
                    style: buttonStyle,
                    onClick: (e) => {
                        e.stopPropagation();
                        this.handleButtonClick(e, '#yuran-pengajian');
                    }
                },
                    React.createElement('i', { className: "fas fa-dollar-sign me-1" }),
                    "YURAN"
                ),
                
                React.createElement(Button, {
                    variant: "outline-info",
                    style: buttonStyle,
                    onClick: (e) => {
                        e.stopPropagation();
                        this.handleButtonClick(e, '#kampus__' + programData.kodProgram);
                    }
                },
                    React.createElement('i', { className: "fas fa-map-marker-alt me-1" }),
                    "KAMPUS"
                ),
                
                React.createElement(Button, {
                    variant: "outline-warning",
                    style: buttonStyle,
                    onClick: (e) => {
                        e.stopPropagation();
                        this.handleButtonClick(e, '#laluan-kerjaya_' + programData.kodProgram);
                    }
                },
                    React.createElement('i', { className: "fas fa-briefcase me-1" }),
                    "KERJAYA"
                ),
                
                React.createElement(Button, {
                    variant: "outline-secondary",
                    style: buttonStyle,
                    onClick: (e) => {
                        e.stopPropagation();
                        this.handleButtonClick(e, '#maklumatProgram__' + programData.kodProgram);
                    }
                },
                    React.createElement('i', { className: "fas fa-info-circle me-1" }),
                    "INFO"
                )
            )
        );
    }
}

// Function to render program card
function renderProgramCard(containerId, programData) {
    const container = document.getElementById(containerId);
    if (container) {
        ReactDOM.render(React.createElement(ProgramCard, { programData: programData }), container);
    }
}

// Export for global use
window.ProgramCard = ProgramCard;
window.renderProgramCard = renderProgramCard;
