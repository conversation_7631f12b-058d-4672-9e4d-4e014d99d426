<?php $__env->startSection('content'); ?>
    <section class="bg-half bg-light d-table w-100">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-12 text-center">
                    <div class="page-next-level">
                        <h3 class="title"> PROGRAM PENGAJIAN</h3>
                        <div class="page-next">
                            <nav aria-label="breadcrumb" class="d-inline-block">
                                <ul class="breadcrumb bg-white rounded shadow mb-0">
                                    <li class="breadcrumb-item"><a href="<?php echo e(url('/')); ?>"><PERSON><PERSON></a></li>
                                    <li class="breadcrumb-item"><a
                                            href="<?php echo e(url('kategoriCalon', [session('jenprog')])); ?>">Kategori
                                            <?php echo e(Request::route('kodkatag')); ?></a></li>
                                    <li class="breadcrumb-item"><a href="#">Program Pengajian</a></li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="position-relative">
        <div class="shape overflow-hidden text-white">
            <svg viewBox="0 0 2880 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 48H1437.5H2880V0H2160C1442.5 52 720 0 720 0H0V48Z" fill="currentColor"></path>
            </svg>
        </div>
    </div>

    <section class="section">
        <div class="container">
            <form action="<?php echo e(url('ProgramPengajian/kategoriCalon/' . Request::route('kodkatag'))); ?>" method="post">
                <?php echo csrf_field(); ?>
                <div class="row">
                    <div class="col-lg-3 col-md-4 col-12">
                        <div class="card border-0 sidebar sticky-bar">
                            <div class="card-body p-0">
                                <!-- SEARCH -->
                                <div class="widget">
                                    <div id="search2" class="widget-search mb-0">
                                        <form class="searchform">
                                            <div>
                                                <input type="text" class="border rounded" id="fuzzySearch"
                                                    name="fuzzySearch" placeholder="Carian Program..."
                                                    value="<?php echo e(old('fuzzySearch')); ?>">
                                            </div>
                                        </form>
                                    </div>
                                </div>

                                <div class="widget mt-4 pt-2">
                                    <div class="row" style="margin-bottom: -1.3rem">
                                        <div class="column"
                                            style="float: left;
                                    width: 50%;
                                    margin-top: 6px; margin-left: 16px; margin-right: -25px;">
                                            <h5 class="widget-title">Tapisan</h5>
                                        </div>
                                        <div class="uncheck-clear-button" style="text-align: right">
                                            <button type="submit" class="badge badge-pill badge-success" id="searching" name="searching" style="border: 1px solid #000; padding-left: 12px; padding-right: 12px;;"><i class="fas fa-search"></i> Cari</button>
                                            <button type="submit" class="badge badge-pill badge-danger" id="clearFiltering" name="clearFiltering" style="border: 1px solid #000; padding-left: 9px; padding-right: 9px;"><i class="fas fa-trash-alt"></i> Clear</button>
                                        </div>
                                    </div>

                                    <ul class="list-unstyled mt-4 mb-0 blog-categories sidebar-menu">
                                        <li class="have-children"><a href="#"><span class="fa fa-th-list"
                                                    style="padding-right: 7px;"></span>Bidang</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" name="pBidang[]" value="00"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('00', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck1">
                                                                <label class="custom-control-label"
                                                                    for="customCheck1">Program Dan Kelayakan Generik</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="01" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('01', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck2">
                                                                <label class="custom-control-label"
                                                                    for="customCheck2">Pendidikan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="02" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('02', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck4">
                                                                <label class="custom-control-label"
                                                                    for="customCheck4">Sastera
                                                                    Dan Kemanusiaan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="03" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('03', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck5">
                                                                <label class="custom-control-label"
                                                                    for="customCheck5">Sains
                                                                    Sosial, Kewartawanan Dan Maklumat</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="04" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('04', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck6">
                                                                <label class="custom-control-label"
                                                                    for="customCheck6">Perniagaan, Pentadbiran Dan
                                                                    Perundangan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="05" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('05', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck7">
                                                                <label class="custom-control-label"
                                                                    for="customCheck7">Sains Semulajadi, Matematik Dan
                                                                    Statistik</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="06" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('06', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck8">
                                                                <label class="custom-control-label"
                                                                    for="customCheck8">Teknologi Maklumat Dan
                                                                    Komunikasi</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="07" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('07', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck9">
                                                                <label class="custom-control-label"
                                                                    for="customCheck9">Kejuruteraan, Pembuatan Dan
                                                                    Pembinaan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="08" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('08', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck10">
                                                                <label class="custom-control-label"
                                                                    for="customCheck10">Pertanian, Perhutanan, Perikanan
                                                                    Dan Vaterinar</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="09" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('09', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck11">
                                                                <label class="custom-control-label"
                                                                    for="customCheck11">Kesihatan Dan kebajikan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="10" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('10', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck12">
                                                                <label class="custom-control-label"
                                                                    for="customCheck12">Perkhidmatan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <li class="have-children"><a href="#"><span class="fa fa-university"
                                                    style="padding-right: 7px;"></span>IPTA</a>
                                            <ul>
                                                <?php if(session()->get('jenprog') == 'spm'): ?>
                                                    
                                                    
                                                    
                                                    
                                                    
                                                    
                                                    
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="OM"
                                                                        name="carianIPTA[]"
                                                                        <?php if(!empty(session()->get('jIPTA')) && in_array('OM', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA30">
                                                                    <label class="custom-control-label" for="carianIPTA30">Kolej Mara</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="FC"
                                                                        name="carianIPTA[]"
                                                                        <?php if(!empty(session()->get('jIPTA')) && in_array('FC', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA2">
                                                                    <label class="custom-control-label" for="carianIPTA2">Kolej Komuniti</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="OP"
                                                                        name="carianIPTA[]"
                                                                        <?php if(!empty(session()->get('jIPTA')) && in_array('OP', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA31">
                                                                    <label class="custom-control-label" for="carianIPTA31">KPM</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="FB"
                                                                        name="carianIPTA[]"
                                                                        <?php if(!empty(session()->get('jIPTA')) && in_array('FB', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA1">
                                                                    <label class="custom-control-label"
                                                                        for="carianIPTA1">Politeknik</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                <?php endif; ?>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UY" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UY', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA27">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA27">UIAM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UE" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UE', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA13">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA13">UITM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UK" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UK', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA17">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA17">UKM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UM" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UM', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA19">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA19">UM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UJ" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UJ', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA16">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA16">UMPSA</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UL" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UL', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA18">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA18">UMK</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UH" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UH', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA15">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA15">UMS</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UG" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UG', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA14">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA14">UMT</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UR" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UR', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA22">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA22">UNIMAP</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UW" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UW', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA26">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA26">UNIMAS</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UD" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UD', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA12">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA12">UNISZA</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UP" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UP', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA20">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA20">UPM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UZ" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UZ', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA28">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA28">UPNM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UA" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UA', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA9">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA9">UPSI</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UQ" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UQ', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA21">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA21">USIM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="US" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('US', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA23">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA23">USM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UT" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UT', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA24">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA24">UTM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UC" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UC', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA11">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA11">UTeM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UB" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UB', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA10">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA10">UTHM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UU" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UU', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA25">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA25">UUM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <?php if(session()->get('jenprog') == 'spm'): ?>
                                            <li class="have-children"><a href="#"><span class="fa fa-code-fork"
                                                        style="padding-right: 7px;"></span>Peringkat Pengajian</a>
                                                <ul>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" name="peringkatPengajian[]"
                                                                        value="0"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('0', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck37">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck37">Asasi/Matrikulasi</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="1"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('1', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck38">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck38">Sijil-Kredit Graduan Min
                                                                        15</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="2"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('2', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck39">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck39">Sijil-Kredit Graduan Min
                                                                        30</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="3"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('3', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck40">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck40">Sijil-Kredit Graduan Min
                                                                        60</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="4"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('4', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck41">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck41">Diploma</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="5"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('5', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck42">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck42">Diploma Lanjutan</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="6"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('6', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck43">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck43">Sarjana Muda</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </li>
                                        <?php endif; ?>
                                        
                                        <li class="have-children"><a href="#"><span class="fa fa-cogs"
                                                    style="padding-right: 7px;"></span>Program TVET</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="Y" name="pTVET[]"
                                                                    <?php if(!empty(session()->get('jTVET')) && in_array('Y', session()->get('jTVET'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck33">
                                                                <label class="custom-control-label"
                                                                    for="customCheck33">Ya</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="T" name="pTVET[]"
                                                                    <?php if(!empty(session()->get('jTVET')) && in_array('T', session()->get('jTVET'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck34">
                                                                <label class="custom-control-label"
                                                                    for="customCheck34">Tidak</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <li class="have-children"><a href="#"><span class="fa fa-pie-chart"
                                                    style="padding-right: 7px;"></span>Mod Pengajian</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="Y"
                                                                    name="ModPengajian[]"
                                                                    <?php if(!empty(session()->get('jMODpengajian')) && in_array('Y', session()->get('jMODpengajian'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck35">
                                                                <label class="custom-control-label"
                                                                    for="customCheck35">2U2I/3U1I</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="T"
                                                                    name="ModPengajian[]"
                                                                    <?php if(!empty(session()->get('jMODpengajian')) && in_array('T', session()->get('jMODpengajian'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck36">
                                                                <label class="custom-control-label"
                                                                    for="customCheck36">Konvensional</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <?php if(session()->get('jenprog') == 'stpm'): ?>
                                            <li class="have-children"><a href="#"><span class="fa fa-certificate"
                                                        style="padding-right: 7px;"></span>Joint/Dual/Double Degree</a>
                                                <ul>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="Y"
                                                                        name="pDoubleDegree[]"
                                                                        <?php if(!empty(session()->get('jDOUBLE_DEGREE')) && in_array('Y', session()->get('jDOUBLE_DEGREE'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck44">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck44">Ya</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="T"
                                                                        name="pDoubleDegree[]"
                                                                        <?php if(!empty(session()->get('jDOUBLE_DEGREE')) && in_array('T', session()->get('jDOUBLE_DEGREE'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck45">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck45">Tidak</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </li>
                                        <?php endif; ?>
                                        <li class="have-children"><a href="#"><span class="fa fa-hashtag"
                                                    style="padding-right: 7px;"></span>Bertemu duga / Ujian</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="Y" name="pTemuduga[]"
                                                                    <?php if(!empty(session()->get('jTEMUDUGA')) && in_array('Y', session()->get('jTEMUDUGA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck31">
                                                                <label class="custom-control-label"
                                                                    for="customCheck31">Ya</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="T" name="pTemuduga[]"
                                                                    <?php if(!empty(session()->get('jTEMUDUGA')) && in_array('T', session()->get('jTEMUDUGA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck32">
                                                                <label class="custom-control-label"
                                                                    for="customCheck32">Tidak</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>

										<?php if(substr(Request::route('kodkatag'),0,1)!='G' && substr(Request::route('kodkatag'),0,1)!='E' && substr(Request::route('kodkatag'),0,1)!='F'): ?>
                                        <li class="have-children"><a href="#"><span class="fa fa-line-chart"
                                                    style="padding-right: 7px;"></span>Minimum Purata Merit</a>
                                            <ul>
                                                <li>
                                                    <input type="range" min="0" max="100" value="0.00"
                                                        step="0.01" style="width: 60%" id="meritProgram"
                                                        name="meritProgram">
                                                    <b><label id="rangeDisplay" style="padding-left: 1rem"></label>%</b>
                                                </li>
                                            </ul>
                                        </li>
										<?php endif; ?>
                                        
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-9 col-md-8 col-12 mt-5 pt-2 mt-sm-0 pt-sm-0">
                        <div class="row align-items-center">
                            <div class="col-lg-8 col-md-7">
                                <div class="section-title">
                                    <h5 class="mb-0">Paparan
                                        <?php echo e($SENARAI_PROGRAM->firstItem()); ?> - <?php echo e($SENARAI_PROGRAM->lastItem()); ?> daripada
                                        <?php echo e($SENARAI_PROGRAM->total()); ?> carian</h5>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <?php if(count($SENARAI_PROGRAM) != '0'): ?>
                                <?php $__currentLoopData = $SENARAI_PROGRAM; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $PROGRAM): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-12 mt-4 pt-2">
                                        <div class="card executive-card shop-list border-0 shadow-lg position-relative">
                                            <div class="row align-items-center no-gutters">
                                                <div class="col-lg-4 col-md-6">
                                                    <?php echo $__env->make('programPengajian.logoUA-ILKA', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                </div>

                                                <div class="col-lg-8 col-md-6">
                                                    <div class="card-body executive-card-body content p-4">
                                                        <a class="text-dark product-name h5"><b><?php echo e($PROGRAM->nama_Program); ?>

                                                                <?php if($PROGRAM->program_Temuduga == 'Y'): ?>
                                                                    <b>#</b>
                                                                <?php endif; ?>

                                                            </b></a>
                                                        <div class="d-lg-flex align-items-center mt-2 mb-3">
                                                            <h6 class="text-muted small mb-0 mr-3"
                                                                style="margin-top: -0.8rem">
                                                                <?php echo e(Str::title($PROGRAM->nama_IPTA)); ?>

                                                            </h6>
                                                        </div>

														<div class="mt-n3 mb-n2">
														<?php if(session()->get('jenprog') == 'spm'): ?>
															<?php if($PROGRAM->program_FEEDER == 'Y'): ?>
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-warning">
																<b>Program Perintis (Feeder)</b>
																	<i class="fas fa-info-circle text-light"
																	data-toggle="tooltip"
																	data-placement="right"
																	data-html="true"
																	title="Program pembekal kepada program Ijazah Sarjana Muda di Universiti Awam berkenaan sahaja."></i>
																</span>
															<?php endif; ?>
															<?php if($PROGRAM->program_STEM == 'Y'): ?>
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-warning"><b>STEM</b></span>
															<?php endif; ?>
															<?php if($PROGRAM->program_TVET == 'Y'): ?>
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-warning">
																<b>TVET</b>
																	<i class="fas fa-info-circle text-light"
																	data-toggle="tooltip"
																	data-placement="right"
																	data-html="true"
																	title="Program pengajian yang berfokus kepada pembangunan bakat holistik iaitu memiliki pengetahuan teknikal, kemahiran dan insaniah."></i>
																</span>
															<?php endif; ?>
														<?php endif; ?>

														<?php if(session()->get('jenprog') == 'stpm'): ?>
															<?php if($PROGRAM->program_KOMPETITIF == 'Y'): ?>
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-danger"><b>Kompetitif</b></span>
															<?php endif; ?>
															<?php if($PROGRAM->program_BTECH == 'Y'): ?>
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-warning"><b>BTECH</b></span>
															<?php endif; ?>
															<?php if($PROGRAM->program_TVET == 'Y'): ?>
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-warning">
																	<b>TVET</b>
																	<i class="fas fa-info-circle text-light"
																	data-toggle="tooltip"
																	data-placement="right"
																	data-html="true"
																	title="Program pengajian yang berfokus kepada pembangunan bakat holistik iaitu memiliki pengetahuan teknikal, kemahiran dan insaniah."></i>
																</span>
															<?php endif; ?>
														<?php endif; ?>
														</div>

                                                        
                                                        

                                                        <div class="form-row mt-4">
                                                            <div class="form-group col-md-4 mb-0">
                                                                <h6 class="border-bottom badge-line text-center">
                                                                    <label class="mb-n2 badge badge-deeppink rounded-sm col-md-12"><i class="fas fa-tag"></i> Kod Program</label>
                                                                    <small><b><?php echo e($PROGRAM->kod_Program); ?></b></small>
                                                                </h6>
                                                            </div>

                                                            <div class="form-group col-md-4 mb-0">
                                                                <h6 class="border-bottom badge-line text-center">
                                                                    <label class="mb-n2 badge badge-deeppink rounded-sm col-md-12"><i class="far fa-calendar-alt"></i> Tahun</label>
                                                                    <small><b><?php echo e(session()->get('tahun_semasa')); ?></b></small>
                                                                </h6>
                                                            </div>

                                                            

                                                            <?php if(substr(Request::route('kodkatag'),0,1)!='G' && substr(Request::route('kodkatag'),0,1)!='E' && substr(Request::route('kodkatag'),0,1)!='F'): ?>
                                                            <div class="form-group col-md-4">
                                                                <h6 class="border-bottom badge-line text-center">
                                                                    <label class="mb-n2 badge badge-deeppink rounded-sm col-md-12">
                                                                        <i class="fas fa-calculator"></i> Purata Markah Merit</label>
                                                                    <small>
                                                                        <?php $__currentLoopData = $MAKLUMAT_PENGAJIAN; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $maklumat_Pengajian): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <?php if($maklumat_Pengajian->kod_Program == $PROGRAM->kod_Program): ?>
                                                                                <?php if($maklumat_Pengajian->kategori_Pengajian == 'T'): ?>
                                                                                    <b><?php if($maklumat_Pengajian->merit_Program==''): ?> Tiada <?php else: ?> <?php echo $maklumat_Pengajian->merit_Program; ?>%
                                                                                        <i class="fas fa-info-circle text-info"
                                                                                        data-toggle="tooltip"
                                                                                        data-placement="right"
                                                                                        data-html="true"
                                                                                        title="<b><u>Penafian</u></b> <br> Tiada jaminan mendapat tawaran berdasarkan markah merit semata-mata. Kejayaan mendapat tawaran bergantung kepada kedudukan merit, memenuhi syarat am dan syarat khas, bilangan tempat yang disediakan dan lulus temu duga dan/atau ujian bagi program pengajian yang menetapkan keperluan berkenaan."></i>
                                                                                        <?php endif; ?>
                                                                                    </b>
                                                                                <?php endif; ?>

                                                                                <?php if($maklumat_Pengajian->kategori_Pengajian != 'T'): ?>
                                                                                    <b>
                                                                                        <?php if($maklumat_Pengajian->merit_Program==''): ?> Tiada <?php else: ?> <?php echo $maklumat_Pengajian->merit_Program; ?>%
                                                                                        <i class="fas fa-info-circle text-info"
                                                                                        data-toggle="tooltip"
                                                                                        data-placement="right"
                                                                                        data-html="true"
                                                                                        title="<b><u>Penafian</u></b> <br> Tiada jaminan mendapat tawaran berdasarkan markah merit semata-mata. Kejayaan mendapat tawaran bergantung kepada kedudukan merit, memenuhi syarat am dan syarat khas, bilangan tempat yang disediakan dan lulus temu duga dan/atau ujian bagi program pengajian yang menetapkan keperluan berkenaan."></i>
                                                                                        <?php endif; ?>
                                                                                    </b>
                                                                                <?php endif; ?>

                                                                            <?php endif; ?>
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    </small>

                                                                </h6>
                                                            </div>
                                                            <?php endif; ?>
                                                        </div>

                                                        <!-- Executive Action Buttons - Single Row -->
                                                        <div class="executive-buttons-container mt-3 mb-3">
                                                            <div class="d-flex flex-wrap gap-2 justify-content-start">
                                                                <!-- Syarat Program Button -->
                                                                <?php if(session()->get('jenprog') == 'spm'): ?>
                                                                    <a href="<?php echo e(url('ProgramPengajian/modalSyarat/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->kod_Program)); ?>"
                                                                        id="kelayakanMinimum_Modal" data-toggle="modal" data-target="#subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                        class="btn-executive btn-executive-primary" data-tooltip="Syarat Program">
                                                                        <i class="fas fa-book me-2"></i>
                                                                        <span>Syarat</span>
                                                                    </a>
                                                                <?php elseif(session()->get('jenprog') == 'stpm'): ?>
                                                                    <?php if($PROGRAM->kategori_Pengajian=='G' || $PROGRAM->kategori_Pengajian=='E' || $PROGRAM->kategori_Pengajian=='F'): ?>
                                                                        <a href="<?php echo e(url('ProgramPengajian/modalSyaratdiploma/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->jensetaraf . '/' . $PROGRAM->kod_Program)); ?>"
                                                                            id="kelayakanMinimum_Modal" data-toggle="modal" data-target="#subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                            class="btn-executive btn-executive-primary" data-tooltip="Syarat Program">
                                                                            <i class="fas fa-book me-2"></i>
                                                                            <span>Syarat</span>
                                                                        </a>
                                                                    <?php else: ?>
                                                                        <a href="<?php echo e(url('ProgramPengajian/modalSyarat/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->kod_Program)); ?>"
                                                                            id="kelayakanMinimum_Modal" data-toggle="modal" data-target="#subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                            class="btn-executive btn-executive-primary" data-tooltip="Syarat Program">
                                                                            <i class="fas fa-book me-2"></i>
                                                                            <span>Syarat</span>
                                                                        </a>
                                                                    <?php endif; ?>
                                                                <?php endif; ?>

                                                                <!-- Yuran Pengajian Button -->
                                                                <a href="javascript:void(0)" data-toggle="modal" data-target="#yuran-pengajian"
                                                                    class="btn-executive btn-executive-success" data-tooltip="Yuran Pengajian">
                                                                    <i class="fas fa-dollar-sign me-2"></i>
                                                                    <span>Yuran</span>
                                                                </a>

                                                                <!-- Kampus Button -->
                                                                <a href="javascript:void(0)" data-toggle="modal" data-target="#kampus__<?php echo e($PROGRAM->kod_Program); ?>"
                                                                    class="btn-executive btn-executive-info" data-tooltip="Kampus">
                                                                    <i class="fas fa-map-marked-alt me-2"></i>
                                                                    <span>Kampus</span>
                                                                </a>

                                                                <!-- Laluan Kerjaya Button -->
                                                                <a href="javascript:void(0)" data-toggle="modal" data-target="#laluan-kerjaya_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                    class="btn-executive btn-executive-warning" data-tooltip="Laluan Kerjaya">
                                                                    <i class="fas fa-briefcase me-2"></i>
                                                                    <span>Kerjaya</span>
                                                                </a>

                                                                <!-- Bidang NEC Button (conditional) -->
                                                                <?php if(substr(Request::route('kodkatag'),0,1)=='G'): ?>
                                                                <a href="javascript:void(0)" data-toggle="modal" data-target="#bidangNEC_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                    class="btn-executive btn-executive-secondary" data-tooltip="Bidang NEC">
                                                                    <i class="fas fa-bullseye me-2"></i>
                                                                    <span>NEC</span>
                                                                </a>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>








                                                        <ul class="list-unstyled mb-0 flex-wrap">
                                                           
                                                           
                                                            <?php if(session()->get('jenprog') == 'spm'): ?>
                                                              
                                                                <div class="modal fade kelayakanMinimum_Modal"
                                                                    id="subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                    tabindex="-1" role="dialog"
                                                                    aria-labelledby="syarat-program-title"
                                                                    aria-hidden="true">
                                                                    <div class="modal-dialog modal-dialog-centered modal-llgg"
                                                                        role="document">
                                                                        <div
                                                                            class="modal-content rounded shadow-lg border-0 overflow-hidden">
                                                                            <button type="button"
                                                                                class="close float-right mr-2"
                                                                                data-dismiss="modal" aria-label="Close"
                                                                                style="display:flex; justify-content:flex-end; width:100%; padding:0.9rem; margin-top: -0.8rem;">
                                                                                <span aria-hidden="true">&times;</span>
                                                                            </button>
                                                                            <div class="modal-body" style="height: 90vh; overflow-y: auto;">
                                                                                <div class="text-left">
                                                                                    <h4 class="text-center"><b>Syarat
                                                                                            Program</b>
                                                                                    </h4>
                                                                                    <div class="container mt-100 mt-60">
                                                                                        <div class="row">
                                                                                            <div class="col-12">
                                                                                                <ul class="nav nav-pills shadow flex-column flex-sm-row mb-0 p-1 bg-white rounded overflow-hidden"
                                                                                                    id="pills-tab"
                                                                                                    role="tablist">
                                                                                                    <!--Syarat Am Tab-->
                                                                                                    <li
                                                                                                        class="nav-item col-sm-6">
                                                                                                        <a class="nav-link py-2 active rounded"
                                                                                                            id="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            data-toggle="pill"
                                                                                                            href="#syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            role="tab"
                                                                                                            aria-controls="syarat-am"
                                                                                                            aria-selected="false">
                                                                                                            <div
                                                                                                                class="text-center">
                                                                                                                <h6
                                                                                                                    class="mb-0">
                                                                                                                    Syarat Am</h6>
                                                                                                            </div>
                                                                                                        </a>
                                                                                                    </li>

                                                                                                    <!--Syarat Khas Tab-->
                                                                                                    <li
                                                                                                        class="nav-item col-sm-6">
                                                                                                        <a class="nav-link py-2 rounded"
                                                                                                            id="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            data-toggle="pill"
                                                                                                            href="#syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            role="tab"
                                                                                                            aria-controls="syarat-Khas"
                                                                                                            aria-selected="false">
                                                                                                            <div
                                                                                                                class="text-center">
                                                                                                                <h6
                                                                                                                    class="mb-0">
                                                                                                                    Syarat Khas
                                                                                                                </h6>
                                                                                                            </div>
                                                                                                        </a>
                                                                                                    </li>
                                                                                                </ul>

                                                                                                <div class="tab-content"
                                                                                                    id="pills-tabContent"
                                                                                                    style="padding-top: 2rem!important">
                                                                                                    <!--Paparan Syarat Am Tab-->
                                                                                                    <div class="card border-0 tab-pane fade show active"
                                                                                                        id="syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tabpanel"
                                                                                                        aria-labelledby="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>">

                                                                                                        <?php echo $__env->make('programPengajian.syarat_am_spm', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                        

                                                                                                    </div>

                                                                                                    <!--Paparan Syarat khas Tab-->
                                                                                                    <div class="card border-0 tab-pane fade"
                                                                                                        id="syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tabpanel"
                                                                                                        aria-labelledby="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>">
                                                                                                        <div class="text-muted contentLoad"
                                                                                                            style="font-weight: bold">
                                                                                                            <div
                                                                                                                align="center">
                                                                                                                <div
                                                                                                                    class="loader-spinner text-center">
                                                                                                                </div>
                                                                                                                <h4>Sila
                                                                                                                    tunggu
                                                                                                                    sebentar...
                                                                                                                </h4>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>



                                                            <?php elseif(session()->get('jenprog') == 'stpm'): ?>



                                                           

                                                            <div class="modal fade kelayakanMinimum_Modal"
                                                                id="subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                tabindex="-1" role="dialog"
                                                                aria-labelledby="syarat-program-title"
                                                                aria-hidden="true">
                                                                <div class="modal-dialog modal-dialog-centered modal-llgg"
                                                                    role="document" >
                                                                    <div
                                                                        class="modal-content rounded shadow-lg border-0 overflow-hidden">
                                                                        <button type="button"
                                                                            class="close float-right mr-2"
                                                                            data-dismiss="modal" aria-label="Close"
                                                                            style="display:flex; justify-content:flex-end; width:100%; padding:0.9rem; margin-top: -0.8rem;">
                                                                            <span aria-hidden="true">&times;</span>
                                                                        </button>
                                                                        <div class="modal-body" style="height: 90vh; overflow-y: auto;">
                                                                            <div class="text-left">
                                                                                <h4 class="text-center"><b>Syarat
                                                                                        Program</b>
                                                                                </h4>
                                                                                <div class="container mt-100 mt-60">
                                                                                    <div class="row">
                                                                                        <div class="col-12">
                                                                                            <ul class="nav nav-pills shadow flex-column flex-sm-row mb-0 p-1 bg-white rounded overflow-hidden"
                                                                                                id="pills-tab"
                                                                                                role="tablist">
                                                                                                <!--Syarat Am Tab-->
                                                                                                <li <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?> class="nav-item col-4" <?php else: ?> class="nav-item col-6" <?php endif; ?>>
                                                                                                    <a class="nav-link py-2 active rounded"
                                                                                                        id="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-am"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Am</h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>

                                                                                                <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?>
                                                                                                <li <?php echo e($PROGRAM->kod_Program == 'UM6143001' ? 'hidden' : null); ?>

                                                                                                    class="nav-item col-4">
                                                                                                    <a class="nav-link py-2 rounded"
                                                                                                        id="syarat-pendidikan-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-pendidikan-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-pendidikan"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Pendidikan</h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>
                                                                                                <?php endif; ?>


                                                                                                <!--Syarat Khas Tab-->
                                                                                                <li <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?> class="nav-item col-4" <?php else: ?> class="nav-item col-6" <?php endif; ?>>
                                                                                                    <a class="nav-link py-2 rounded"
                                                                                                        id="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-Khas"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Khas
                                                                                                            </h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>
                                                                                            </ul>

                                                                                            <div class="tab-content"
                                                                                                id="pills-tabContent"
                                                                                                style="padding-top: 2rem!important">
                                                                                                <!--Paparan Syarat Am Tab-->
                                                                                                <div class="card border-0 tab-pane fade show active"
                                                                                                    id="syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>">

                                                                                                    <?php if($PROGRAM->kategori_Pengajian=='G'): ?>
																										<?php echo $__env->make('programPengajian.syarat_am_diploma_g', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php elseif($PROGRAM->kategori_Pengajian=='E'): ?>
																										<?php echo $__env->make('programPengajian.syarat_am_diploma_e', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php elseif($PROGRAM->kategori_Pengajian=='F'): ?>
																										<?php echo $__env->make('programPengajian.syarat_am_diploma_f', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php else: ?>
																										<?php echo $__env->make('programPengajian.syarat_am_stpm', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php endif; ?>

                                                                                                </div>

                                                                                                <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?>
                                                                                                <div class="card border-0 tab-pane fade"
                                                                                                    id="syarat-pendidikan-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-pendidikan-tab-<?php echo e($PROGRAM->kod_Program); ?>">

                                                                                                    <?php echo $__env->make('programPengajian.syarat_am_pendidikan', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                </div>
                                                                                                <?php endif; ?>


                                                                                                <!--Paparan Syarat khas Tab-->
                                                                                                <div class="card border-0 tab-pane fade"
                                                                                                    id="syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>">
                                                                                                    <div class="text-muted contentLoad"
                                                                                                        style="font-weight: bold">
                                                                                                        <div
                                                                                                            align="center">
                                                                                                            <div
                                                                                                                class="loader-spinner text-center">
                                                                                                            </div>
                                                                                                            <h4>Sila
                                                                                                                tunggu
                                                                                                                sebentar...
                                                                                                            </h4>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>





                                                                
                                                            <?php endif; ?>
                                                            

                                                            
                                                            

                                                            <li class="mt-0 mb-4 list-inline-item"
                                                                style="float: right; padding-top: 0.5rem">
                                                                <a type="button" class="text-dark" data-toggle="modal"
                                                                    data-target="#maklumatProgram__<?php echo e($PROGRAM->kod_Program); ?>">
                                                                    Maklumat program
                                                                    <i data-feather="arrow-right" class="icons"></i></a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>

                                                <!--end col-->
                                            </div>
                                            <!--end row-->
                                        </div>
                                        <!--end blog post-->
                                    </div>
                                    <?php echo $__env->make('programPengajian.modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <?php echo $__env->make('pageLock.tiadaMaklumat', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            <?php endif; ?>

                            <!-- PAGINATION START -->
                            <div class="col-12 mt-4 pt-2">
                                <?php echo $SENARAI_PROGRAM->links('programPengajian.list-paginator'); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>
    <script src="<?php echo e(asset('/assets/js/range-slider.js')); ?>"></script>

    <!-- Executive Modern Styling -->
    <style>
        /* Executive Card Styling */
        .executive-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .executive-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }

        .executive-card-body {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 0 12px 12px 0;
            position: relative;
        }

        .executive-card-body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #2c3e50 0%, #3498db 50%, #27ae60 100%);
        }

        /* Executive Button Styling */
        .btn-executive {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none !important;
            font-weight: 600;
            font-size: 12px;
            border: 1px solid;
            background: #ffffff;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            min-width: 85px;
            height: 36px;
            justify-content: center;
            letter-spacing: 0.3px;
            text-transform: uppercase;
        }

        .btn-executive:hover {
            transform: translateY(-2px);
            text-decoration: none !important;
        }

        .btn-executive:focus {
            text-decoration: none !important;
            outline: none;
        }

        .executive-buttons-container {
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 8px;
            margin: 15px 0;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .btn-compact:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            text-decoration: none !important;
        }

        .btn-compact:active {
            transform: translateY(0) scale(0.98);
            transition: transform 0.1s;
        }

        .btn-compact:focus {
            text-decoration: none !important;
            outline: none;
        }

        /* Button Icon */
        .btn-compact-icon {
            margin-right: 6px;
            font-size: 14px;
            transition: transform 0.3s ease;
        }

        .btn-compact:hover .btn-compact-icon {
            transform: scale(1.1) rotate(5deg);
        }

        /* Button Text */
        .btn-compact-text {
            font-weight: 600;
            letter-spacing: 0.3px;
            white-space: nowrap;
        }

        /* Executive Button Color Variants */
        .btn-executive-primary {
            border-color: #1a252f;
            color: #1a252f;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .btn-executive-primary:hover {
            background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
            color: white !important;
            border-color: #1a252f;
            text-decoration: none !important;
            box-shadow: 0 6px 20px rgba(26, 37, 47, 0.3);
        }

        .btn-executive-success {
            border-color: #1e8449;
            color: #1e8449;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .btn-executive-success:hover {
            background: linear-gradient(135deg, #1e8449 0%, #27ae60 100%);
            color: white !important;
            border-color: #1e8449;
            text-decoration: none !important;
            box-shadow: 0 6px 20px rgba(30, 132, 73, 0.3);
        }

        .btn-executive-info {
            border-color: #2874a6;
            color: #2874a6;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .btn-executive-info:hover {
            background: linear-gradient(135deg, #2874a6 0%, #3498db 100%);
            color: white !important;
            border-color: #2874a6;
            text-decoration: none !important;
            box-shadow: 0 6px 20px rgba(40, 116, 166, 0.3);
        }

        .btn-executive-warning {
            border-color: #d68910;
            color: #d68910;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .btn-executive-warning:hover {
            background: linear-gradient(135deg, #d68910 0%, #f39c12 100%);
            color: white !important;
            border-color: #d68910;
            text-decoration: none !important;
            box-shadow: 0 6px 20px rgba(214, 137, 16, 0.3);
        }

        .btn-executive-secondary {
            border-color: #566573;
            color: #566573;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .btn-executive-secondary:hover {
            background: linear-gradient(135deg, #566573 0%, #95a5a6 100%);
            color: white !important;
            border-color: #566573;
            text-decoration: none !important;
            box-shadow: 0 6px 20px rgba(86, 101, 115, 0.3);
        }

        /* Simplified Tooltip Styling */
        .btn-compact[data-tooltip] {
            position: relative;
        }

        .btn-compact[data-tooltip]:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 120%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        @keyframes  fadeIn {
            from { opacity: 0; transform: translateX(-50%) translateY(10px); }
            to { opacity: 1; transform: translateX(-50%) translateY(0); }
        }

        /* Action Buttons Container */
        .action-buttons-container {
            margin: 0;
        }

        /* Executive Responsive Design */
        @media (max-width: 768px) {
            .btn-executive {
                padding: 6px 12px;
                font-size: 11px;
                min-width: 75px;
                height: 32px;
            }

            .executive-buttons-container {
                padding: 15px;
                margin: 10px 0;
            }

            .executive-card {
                border-radius: 8px;
            }

            .executive-card-body {
                border-radius: 0 8px 8px 0;
            }
        }

        @media (max-width: 480px) {
            .btn-executive {
                min-width: 65px;
                height: 30px;
                font-size: 10px;
                padding: 5px 10px;
            }

            .executive-buttons-container {
                padding: 12px;
            }
        }

        /* Executive Animation Effects */
        .executive-card {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes  fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .btn-executive {
            animation: slideInRight 0.5s ease-out;
        }

        @keyframes  slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Executive Tooltip Enhancement */
        .btn-executive[data-tooltip]:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 120%;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 11px;
            white-space: nowrap;
            z-index: 1000;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            animation: tooltipFadeIn 0.3s ease;
        }

        @keyframes  tooltipFadeIn {
            from { opacity: 0; transform: translateX(-50%) translateY(10px); }
            to { opacity: 1; transform: translateX(-50%) translateY(0); }
        }

        /* Executive Focus States */
        .btn-executive:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.25);
        }

        .btn-executive-success:focus {
            box-shadow: 0 0 0 3px rgba(30, 132, 73, 0.25);
        }

        .btn-executive-info:focus {
            box-shadow: 0 0 0 3px rgba(40, 116, 166, 0.25);
        }

        .btn-executive-warning:focus {
            box-shadow: 0 0 0 3px rgba(214, 137, 16, 0.25);
        }

        .btn-executive-secondary:focus {
            box-shadow: 0 0 0 3px rgba(86, 101, 115, 0.25);
        }

        /* Remove any unwanted effects */
        .btn-executive,
        .btn-executive:hover,
        .btn-executive:focus,
        .btn-executive:active,
        .btn-executive:visited {
            text-decoration: none !important;
            border-bottom: none !important;
        }

        .btn-executive::before,
        .btn-executive::after {
            display: none;
        }

        /* Loading State */
        .btn-compact.loading {
            pointer-events: none;
            opacity: 0.7;
        }

        .btn-compact.loading .btn-compact-icon {
            animation: spin 1s linear infinite;
        }

        @keyframes  spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Focus States for Accessibility */
        .btn-compact:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
        }

        .btn-compact-success:focus {
            box-shadow: 0 0 0 3px rgba(25, 135, 84, 0.25);
        }

        .btn-compact-info:focus {
            box-shadow: 0 0 0 3px rgba(13, 202, 240, 0.25);
        }

        .btn-compact-warning:focus {
            box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.25);
        }

        .btn-compact-secondary:focus {
            box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.25);
        }

        /* Remove any pseudo-elements that might cause black bars */
        .btn-compact::before {
            display: none;
        }

        .btn-compact::after {
            display: none;
        }

        /* Ensure no underlines or text decorations */
        .btn-compact,
        .btn-compact:hover,
        .btn-compact:focus,
        .btn-compact:active,
        .btn-compact:visited {
            text-decoration: none !important;
            border-bottom: none !important;
        }
    </style>

    <!-- Executive Interactive JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Executive button interactions
            const executiveButtons = document.querySelectorAll('.btn-executive');
            const executiveCards = document.querySelectorAll('.executive-card');

            // Enhanced button interactions
            executiveButtons.forEach((button, index) => {
                // Add click animation with loading state
                button.addEventListener('click', function(e) {
                    const icon = this.querySelector('i');
                    const originalIcon = icon.className;

                    // Add loading state
                    this.classList.add('loading');
                    icon.className = 'fas fa-spinner fa-spin';

                    // Create ripple effect
                    const ripple = document.createElement('span');
                    ripple.classList.add('ripple-effect');
                    this.appendChild(ripple);

                    // Remove loading state and ripple
                    setTimeout(() => {
                        this.classList.remove('loading');
                        icon.className = originalIcon;
                        ripple.remove();
                    }, 1000);
                });

                // Staggered entrance animation
                button.style.opacity = '0';
                button.style.transform = 'translateX(20px)';

                setTimeout(() => {
                    button.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                    button.style.opacity = '1';
                    button.style.transform = 'translateX(0)';
                }, index * 150 + 300);

                // Hover sound effect simulation
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px) scale(1.02)';
                });

                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Executive card animations
            executiveCards.forEach((card, index) => {
                // Intersection Observer for scroll animations
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.style.animation = `fadeInUp 0.8s ease-out ${index * 0.1}s both`;
                        }
                    });
                }, {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                });

                observer.observe(card);

                // Card hover effects
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                    this.style.boxShadow = '0 25px 70px rgba(0, 0, 0, 0.2)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1)';
                });
            });

            // Add dynamic ripple effect styles
            const rippleStyle = document.createElement('style');
            rippleStyle.textContent = `
                .ripple-effect {
                    position: absolute;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.6);
                    transform: scale(0);
                    animation: ripple 0.6s linear;
                    pointer-events: none;
                }

                @keyframes  ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }

                .btn-executive.loading {
                    pointer-events: none;
                    opacity: 0.8;
                }

                /* Executive gradient text effect */
                .executive-gradient-text {
                    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                }

                /* Executive glow effect on focus */
                .btn-executive:focus {
                    animation: executiveGlow 2s ease-in-out infinite alternate;
                }

                @keyframes  executiveGlow {
                    from {
                        box-shadow: 0 0 5px rgba(44, 62, 80, 0.5);
                    }
                    to {
                        box-shadow: 0 0 20px rgba(44, 62, 80, 0.8);
                    }
                }
            `;
            document.head.appendChild(rippleStyle);

            // Executive theme color cycling (subtle)
            let colorIndex = 0;
            const colors = ['#2c3e50', '#27ae60', '#3498db', '#f39c12', '#95a5a6'];

            setInterval(() => {
                const cardBodies = document.querySelectorAll('.executive-card-body::before');
                colorIndex = (colorIndex + 1) % colors.length;
            }, 10000); // Change every 10 seconds
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/index.blade.php ENDPATH**/ ?>