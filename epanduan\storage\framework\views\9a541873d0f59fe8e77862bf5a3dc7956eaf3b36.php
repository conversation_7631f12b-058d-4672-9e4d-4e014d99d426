<?php $__env->startSection('content'); ?>
    <section class="bg-half bg-light d-table w-100">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-12 text-center">
                    <div class="page-next-level">
                        <h3 class="title"> PROGRAM PENGAJIAN</h3>
                        <div class="page-next">
                            <nav aria-label="breadcrumb" class="d-inline-block">
                                <ul class="breadcrumb bg-white rounded shadow mb-0">
                                    <li class="breadcrumb-item"><a href="<?php echo e(url('/')); ?>"><PERSON><PERSON></a></li>
                                    <li class="breadcrumb-item"><a
                                            href="<?php echo e(url('kategoriCalon', [session('jenprog')])); ?>">Kategori
                                            <?php echo e(Request::route('kodkatag')); ?></a></li>
                                    <li class="breadcrumb-item"><a href="#">Program Pengajian</a></li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="position-relative">
        <div class="shape overflow-hidden text-white">
            <svg viewBox="0 0 2880 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 48H1437.5H2880V0H2160C1442.5 52 720 0 720 0H0V48Z" fill="currentColor"></path>
            </svg>
        </div>
    </div>

    <section class="section">
        <div class="container">
            <form action="<?php echo e(url('ProgramPengajian/kategoriCalon/' . Request::route('kodkatag'))); ?>" method="post">
                <?php echo csrf_field(); ?>
                <div class="row">
                    <div class="col-lg-3 col-md-4 col-12">
                        <div class="card border-0 sidebar sticky-bar">
                            <div class="card-body p-0">
                                <!-- SEARCH -->
                                <div class="widget">
                                    <div id="search2" class="widget-search mb-0">
                                        <form class="searchform">
                                            <div>
                                                <input type="text" class="border rounded" id="fuzzySearch"
                                                    name="fuzzySearch" placeholder="Carian Program..."
                                                    value="<?php echo e(old('fuzzySearch')); ?>">
                                            </div>
                                        </form>
                                    </div>
                                </div>

                                <div class="widget mt-4 pt-2">
                                    <div class="row" style="margin-bottom: -1.3rem">
                                        <div class="column"
                                            style="float: left;
                                    width: 50%;
                                    margin-top: 6px; margin-left: 16px; margin-right: -25px;">
                                            <h5 class="widget-title">Tapisan</h5>
                                        </div>
                                        <div class="uncheck-clear-button" style="text-align: right">
                                            <button type="submit" class="badge badge-pill badge-success" id="searching" name="searching" style="border: 1px solid #000; padding-left: 12px; padding-right: 12px;;"><i class="fas fa-search"></i> Cari</button>
                                            <button type="submit" class="badge badge-pill badge-danger" id="clearFiltering" name="clearFiltering" style="border: 1px solid #000; padding-left: 9px; padding-right: 9px;"><i class="fas fa-trash-alt"></i> Clear</button>
                                        </div>
                                    </div>

                                    <ul class="list-unstyled mt-4 mb-0 blog-categories sidebar-menu">
                                        <li class="have-children"><a href="#"><span class="fa fa-th-list"
                                                    style="padding-right: 7px;"></span>Bidang</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" name="pBidang[]" value="00"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('00', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck1">
                                                                <label class="custom-control-label"
                                                                    for="customCheck1">Program Dan Kelayakan Generik</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="01" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('01', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck2">
                                                                <label class="custom-control-label"
                                                                    for="customCheck2">Pendidikan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="02" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('02', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck4">
                                                                <label class="custom-control-label"
                                                                    for="customCheck4">Sastera
                                                                    Dan Kemanusiaan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="03" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('03', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck5">
                                                                <label class="custom-control-label"
                                                                    for="customCheck5">Sains
                                                                    Sosial, Kewartawanan Dan Maklumat</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="04" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('04', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck6">
                                                                <label class="custom-control-label"
                                                                    for="customCheck6">Perniagaan, Pentadbiran Dan
                                                                    Perundangan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="05" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('05', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck7">
                                                                <label class="custom-control-label"
                                                                    for="customCheck7">Sains Semulajadi, Matematik Dan
                                                                    Statistik</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="06" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('06', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck8">
                                                                <label class="custom-control-label"
                                                                    for="customCheck8">Teknologi Maklumat Dan
                                                                    Komunikasi</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="07" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('07', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck9">
                                                                <label class="custom-control-label"
                                                                    for="customCheck9">Kejuruteraan, Pembuatan Dan
                                                                    Pembinaan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="08" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('08', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck10">
                                                                <label class="custom-control-label"
                                                                    for="customCheck10">Pertanian, Perhutanan, Perikanan
                                                                    Dan Vaterinar</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="09" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('09', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck11">
                                                                <label class="custom-control-label"
                                                                    for="customCheck11">Kesihatan Dan kebajikan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="10" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('10', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck12">
                                                                <label class="custom-control-label"
                                                                    for="customCheck12">Perkhidmatan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <li class="have-children"><a href="#"><span class="fa fa-university"
                                                    style="padding-right: 7px;"></span>IPTA</a>
                                            <ul>
                                                <?php if(session()->get('jenprog') == 'spm'): ?>
                                                    
                                                    
                                                    
                                                    
                                                    
                                                    
                                                    
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="OM"
                                                                        name="carianIPTA[]"
                                                                        <?php if(!empty(session()->get('jIPTA')) && in_array('OM', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA30">
                                                                    <label class="custom-control-label" for="carianIPTA30">Kolej Mara</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="FC"
                                                                        name="carianIPTA[]"
                                                                        <?php if(!empty(session()->get('jIPTA')) && in_array('FC', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA2">
                                                                    <label class="custom-control-label" for="carianIPTA2">Kolej Komuniti</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="OP"
                                                                        name="carianIPTA[]"
                                                                        <?php if(!empty(session()->get('jIPTA')) && in_array('OP', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA31">
                                                                    <label class="custom-control-label" for="carianIPTA31">KPM</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="FB"
                                                                        name="carianIPTA[]"
                                                                        <?php if(!empty(session()->get('jIPTA')) && in_array('FB', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA1">
                                                                    <label class="custom-control-label"
                                                                        for="carianIPTA1">Politeknik</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                <?php endif; ?>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UY" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UY', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA27">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA27">UIAM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UE" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UE', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA13">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA13">UITM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UK" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UK', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA17">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA17">UKM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UM" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UM', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA19">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA19">UM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UJ" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UJ', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA16">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA16">UMPSA</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UL" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UL', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA18">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA18">UMK</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UH" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UH', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA15">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA15">UMS</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UG" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UG', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA14">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA14">UMT</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UR" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UR', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA22">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA22">UNIMAP</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UW" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UW', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA26">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA26">UNIMAS</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UD" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UD', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA12">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA12">UNISZA</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UP" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UP', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA20">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA20">UPM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UZ" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UZ', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA28">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA28">UPNM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UA" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UA', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA9">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA9">UPSI</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UQ" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UQ', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA21">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA21">USIM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="US" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('US', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA23">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA23">USM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UT" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UT', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA24">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA24">UTM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UC" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UC', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA11">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA11">UTeM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UB" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UB', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA10">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA10">UTHM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UU" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UU', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA25">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA25">UUM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <?php if(session()->get('jenprog') == 'spm'): ?>
                                            <li class="have-children"><a href="#"><span class="fa fa-code-fork"
                                                        style="padding-right: 7px;"></span>Peringkat Pengajian</a>
                                                <ul>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" name="peringkatPengajian[]"
                                                                        value="0"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('0', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck37">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck37">Asasi/Matrikulasi</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="1"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('1', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck38">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck38">Sijil-Kredit Graduan Min
                                                                        15</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="2"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('2', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck39">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck39">Sijil-Kredit Graduan Min
                                                                        30</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="3"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('3', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck40">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck40">Sijil-Kredit Graduan Min
                                                                        60</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="4"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('4', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck41">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck41">Diploma</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="5"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('5', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck42">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck42">Diploma Lanjutan</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="6"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('6', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck43">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck43">Sarjana Muda</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </li>
                                        <?php endif; ?>
                                        
                                        <li class="have-children"><a href="#"><span class="fa fa-cogs"
                                                    style="padding-right: 7px;"></span>Program TVET</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="Y" name="pTVET[]"
                                                                    <?php if(!empty(session()->get('jTVET')) && in_array('Y', session()->get('jTVET'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck33">
                                                                <label class="custom-control-label"
                                                                    for="customCheck33">Ya</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="T" name="pTVET[]"
                                                                    <?php if(!empty(session()->get('jTVET')) && in_array('T', session()->get('jTVET'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck34">
                                                                <label class="custom-control-label"
                                                                    for="customCheck34">Tidak</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <li class="have-children"><a href="#"><span class="fa fa-pie-chart"
                                                    style="padding-right: 7px;"></span>Mod Pengajian</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="Y"
                                                                    name="ModPengajian[]"
                                                                    <?php if(!empty(session()->get('jMODpengajian')) && in_array('Y', session()->get('jMODpengajian'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck35">
                                                                <label class="custom-control-label"
                                                                    for="customCheck35">2U2I/3U1I</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="T"
                                                                    name="ModPengajian[]"
                                                                    <?php if(!empty(session()->get('jMODpengajian')) && in_array('T', session()->get('jMODpengajian'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck36">
                                                                <label class="custom-control-label"
                                                                    for="customCheck36">Konvensional</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <?php if(session()->get('jenprog') == 'stpm'): ?>
                                            <li class="have-children"><a href="#"><span class="fa fa-certificate"
                                                        style="padding-right: 7px;"></span>Joint/Dual/Double Degree</a>
                                                <ul>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="Y"
                                                                        name="pDoubleDegree[]"
                                                                        <?php if(!empty(session()->get('jDOUBLE_DEGREE')) && in_array('Y', session()->get('jDOUBLE_DEGREE'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck44">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck44">Ya</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="T"
                                                                        name="pDoubleDegree[]"
                                                                        <?php if(!empty(session()->get('jDOUBLE_DEGREE')) && in_array('T', session()->get('jDOUBLE_DEGREE'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck45">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck45">Tidak</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </li>
                                        <?php endif; ?>
                                        <li class="have-children"><a href="#"><span class="fa fa-hashtag"
                                                    style="padding-right: 7px;"></span>Bertemu duga / Ujian</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="Y" name="pTemuduga[]"
                                                                    <?php if(!empty(session()->get('jTEMUDUGA')) && in_array('Y', session()->get('jTEMUDUGA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck31">
                                                                <label class="custom-control-label"
                                                                    for="customCheck31">Ya</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="T" name="pTemuduga[]"
                                                                    <?php if(!empty(session()->get('jTEMUDUGA')) && in_array('T', session()->get('jTEMUDUGA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck32">
                                                                <label class="custom-control-label"
                                                                    for="customCheck32">Tidak</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>

										<?php if(substr(Request::route('kodkatag'),0,1)!='G' && substr(Request::route('kodkatag'),0,1)!='E' && substr(Request::route('kodkatag'),0,1)!='F'): ?>
                                        <li class="have-children"><a href="#"><span class="fa fa-line-chart"
                                                    style="padding-right: 7px;"></span>Minimum Purata Merit</a>
                                            <ul>
                                                <li>
                                                    <input type="range" min="0" max="100" value="0.00"
                                                        step="0.01" style="width: 60%" id="meritProgram"
                                                        name="meritProgram">
                                                    <b><label id="rangeDisplay" style="padding-left: 1rem"></label>%</b>
                                                </li>
                                            </ul>
                                        </li>
										<?php endif; ?>
                                        
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-9 col-md-8 col-12 mt-5 pt-2 mt-sm-0 pt-sm-0">
                        <div class="row align-items-center">
                            <div class="col-lg-8 col-md-7">
                                <div class="section-title">
                                    <h5 class="mb-0">Paparan
                                        <?php echo e($SENARAI_PROGRAM->firstItem()); ?> - <?php echo e($SENARAI_PROGRAM->lastItem()); ?> daripada
                                        <?php echo e($SENARAI_PROGRAM->total()); ?> carian</h5>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <?php if(count($SENARAI_PROGRAM) != '0'): ?>
                                <?php $__currentLoopData = $SENARAI_PROGRAM; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $PROGRAM): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-12 mt-4 pt-2">
                                        <div class="card executive-card shop-list border-0 shadow-lg position-relative executive-clickable-card"
                                             data-bs-toggle="modal" data-bs-target="#maklumatProgram__<?php echo e($PROGRAM->kod_Program); ?>"
                                             style="cursor: pointer;">
                                            <div class="row align-items-center no-gutters">
                                                <div class="col-lg-4 col-md-6">
                                                    <?php echo $__env->make('programPengajian.logoUA-ILKA', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                </div>

                                                <div class="col-lg-8 col-md-6">
                                                    <div class="card-body executive-card-body content p-2">
                                                        <!-- Executive Header Section -->
                                                        <div class="executive-header-section">
                                                            <div class="executive-title-container">
                                                                <h5 class="executive-program-title">
                                                                    <?php echo e($PROGRAM->nama_Program); ?>

                                                                    <?php if($PROGRAM->program_Temuduga == 'Y'): ?>
                                                                        <span class="executive-interview-badge">
                                                                            <i class="fas fa-user-tie"></i>
                                                                        </span>
                                                                    <?php endif; ?>
                                                                </h5>
                                                                <p class="executive-institution-name"><?php echo e(Str::title($PROGRAM->nama_IPTA)); ?></p>
                                                            </div>

                                                            <!-- Executive Program Labels -->
                                                            <div class="executive-labels-container">
                                                                <?php if(session()->get('jenprog') == 'spm'): ?>
                                                                    <?php if($PROGRAM->program_FEEDER == 'Y'): ?>
                                                                        <div class="executive-label executive-label-feeder">
                                                                            <div class="executive-label-content">
                                                                                <span class="executive-label-text">Program Perintis (Feeder)</span>
                                                                                <i class="fas fa-info-circle executive-label-icon"
                                                                                   data-toggle="tooltip"
                                                                                   data-placement="top"
                                                                                   title="Program pembekal kepada program Ijazah Sarjana Muda di Universiti Awam berkenaan sahaja."></i>
                                                                            </div>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                    <?php if($PROGRAM->program_STEM == 'Y'): ?>
                                                                        <div class="executive-label executive-label-stem">
                                                                            <div class="executive-label-content">
                                                                                <span class="executive-label-text">STEM</span>
                                                                                <i class="fas fa-atom executive-label-icon"></i>
                                                                            </div>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>

														<div class="mt-n3 mb-n2">
														<?php if(session()->get('jenprog') == 'spm'): ?>
															<?php if($PROGRAM->program_FEEDER == 'Y'): ?>
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-warning">
																<b>Program Perintis (Feeder)</b>
																	<i class="fas fa-info-circle text-light"
																	data-toggle="tooltip"
																	data-placement="right"
																	data-html="true"
																	title="Program pembekal kepada program Ijazah Sarjana Muda di Universiti Awam berkenaan sahaja."></i>
																</span>
															<?php endif; ?>
															<?php if($PROGRAM->program_STEM == 'Y'): ?>
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-warning"><b>STEM</b></span>
															<?php endif; ?>
															<?php if($PROGRAM->program_TVET == 'Y'): ?>
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-warning">
																<b>TVET</b>
																	<i class="fas fa-info-circle text-light"
																	data-toggle="tooltip"
																	data-placement="right"
																	data-html="true"
																	title="Program pengajian yang berfokus kepada pembangunan bakat holistik iaitu memiliki pengetahuan teknikal, kemahiran dan insaniah."></i>
																</span>
															<?php endif; ?>
														<?php endif; ?>

														<?php if(session()->get('jenprog') == 'stpm'): ?>
															<?php if($PROGRAM->program_KOMPETITIF == 'Y'): ?>
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-danger"><b>Kompetitif</b></span>
															<?php endif; ?>
															<?php if($PROGRAM->program_BTECH == 'Y'): ?>
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-warning"><b>BTECH</b></span>
															<?php endif; ?>
															<?php if($PROGRAM->program_TVET == 'Y'): ?>
																<span style="font-size:12px; border: 1px solid #000;" class="badge badge-pill-TVET badge-warning">
																	<b>TVET</b>
																	<i class="fas fa-info-circle text-light"
																	data-toggle="tooltip"
																	data-placement="right"
																	data-html="true"
																	title="Program pengajian yang berfokus kepada pembangunan bakat holistik iaitu memiliki pengetahuan teknikal, kemahiran dan insaniah."></i>
																</span>
															<?php endif; ?>
														<?php endif; ?>
														</div>

                                                        
                                                        

                                                        <!-- Executive Info Cards -->
                                                        <div class="executive-info-cards mt-3">
                                                            <div class="executive-info-card">
                                                                <div class="executive-info-card-header">
                                                                    <i class="fas fa-tag"></i>
                                                                    <span>Kod Program</span>
                                                                </div>
                                                                <div class="executive-info-card-value"><?php echo e($PROGRAM->kod_Program); ?></div>
                                                            </div>

                                                            <div class="executive-info-card">
                                                                <div class="executive-info-card-header">
                                                                    <i class="far fa-calendar-alt"></i>
                                                                    <span>Tahun</span>
                                                                </div>
                                                                <div class="executive-info-card-value"><?php echo e(session()->get('tahun_semasa')); ?></div>
                                                            </div>

                                                            <?php if(substr(Request::route('kodkatag'),0,1)!='G' && substr(Request::route('kodkatag'),0,1)!='E' && substr(Request::route('kodkatag'),0,1)!='F'): ?>
                                                            <div class="executive-info-card">
                                                                <div class="executive-info-card-header">
                                                                    <i class="fas fa-calculator"></i>
                                                                    <span>Merit</span>
                                                                </div>
                                                                <div class="executive-info-card-value">
                                                                    <?php $__currentLoopData = $MAKLUMAT_PENGAJIAN; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $maklumat_Pengajian): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                        <?php if($maklumat_Pengajian->kod_Program == $PROGRAM->kod_Program): ?>
                                                                            <?php if($maklumat_Pengajian->merit_Program==''): ?>
                                                                                Tiada
                                                                            <?php else: ?>
                                                                                <?php echo $maklumat_Pengajian->merit_Program; ?>%
                                                                                <i class="fas fa-info-circle executive-info-tooltip"
                                                                                   data-toggle="tooltip"
                                                                                   data-placement="top"
                                                                                   data-html="true"
                                                                                   title="<b><u>Penafian</u></b> <br> Tiada jaminan mendapat tawaran berdasarkan markah merit semata-mata."></i>
                                                                            <?php endif; ?>
                                                                        <?php endif; ?>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </div>
                                                            </div>
                                                            <?php endif; ?>
                                                        </div>

                                                        <!-- Executive Action Buttons - Single Row -->
                                                        <div class="executive-buttons-container mt-3 mb-3">
                                                            <div class="d-flex flex-nowrap gap-1 justify-content-between align-items-center">
                                                                <!-- Syarat Program Button -->
                                                                <?php if(session()->get('jenprog') == 'spm'): ?>
                                                                    <a href="<?php echo e(url('ProgramPengajian/modalSyarat/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->kod_Program)); ?>"
                                                                        id="kelayakanMinimum_Modal" data-bs-toggle="modal" data-bs-target="#subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                        class="btn-executive btn-executive-primary" data-tooltip="Syarat Program">
                                                                        <i class="fas fa-book me-2"></i>
                                                                        <span>Syarat</span>
                                                                    </a>
                                                                <?php elseif(session()->get('jenprog') == 'stpm'): ?>
                                                                    <?php if($PROGRAM->kategori_Pengajian=='G' || $PROGRAM->kategori_Pengajian=='E' || $PROGRAM->kategori_Pengajian=='F'): ?>
                                                                        <a href="<?php echo e(url('ProgramPengajian/modalSyaratdiploma/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->jensetaraf . '/' . $PROGRAM->kod_Program)); ?>"
                                                                            id="kelayakanMinimum_Modal" data-bs-toggle="modal" data-bs-target="#subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                            class="btn-executive btn-executive-primary" data-tooltip="Syarat Program">
                                                                            <i class="fas fa-book me-2"></i>
                                                                            <span>Syarat</span>
                                                                        </a>
                                                                    <?php else: ?>
                                                                        <a href="<?php echo e(url('ProgramPengajian/modalSyarat/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->kod_Program)); ?>"
                                                                            id="kelayakanMinimum_Modal" data-bs-toggle="modal" data-bs-target="#subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                            class="btn-executive btn-executive-primary" data-tooltip="Syarat Program">
                                                                            <i class="fas fa-book me-2"></i>
                                                                            <span>Syarat</span>
                                                                        </a>
                                                                    <?php endif; ?>
                                                                <?php endif; ?>

                                                                <!-- Yuran Pengajian Button -->
                                                                <a href="javascript:void(0)" data-bs-toggle="modal" data-bs-target="#yuran-pengajian"
                                                                    class="btn-executive btn-executive-success" data-tooltip="Yuran Pengajian">
                                                                    <i class="fas fa-dollar-sign me-2"></i>
                                                                    <span>Yuran</span>
                                                                </a>

                                                                <!-- Kampus Button -->
                                                                <a href="javascript:void(0)" data-bs-toggle="modal" data-bs-target="#kampus__<?php echo e($PROGRAM->kod_Program); ?>"
                                                                    class="btn-executive btn-executive-info" data-tooltip="Kampus">
                                                                    <i class="fas fa-map-marked-alt me-2"></i>
                                                                    <span>Kampus</span>
                                                                </a>

                                                                <!-- Laluan Kerjaya Button -->
                                                                <a href="javascript:void(0)" data-bs-toggle="modal" data-bs-target="#laluan-kerjaya_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                    class="btn-executive btn-executive-warning" data-tooltip="Laluan Kerjaya">
                                                                    <i class="fas fa-briefcase me-2"></i>
                                                                    <span>Kerjaya</span>
                                                                </a>

                                                                <!-- Bidang NEC Button (conditional) -->
                                                                <?php if(substr(Request::route('kodkatag'),0,1)=='G'): ?>
                                                                <a href="javascript:void(0)" data-bs-toggle="modal" data-bs-target="#bidangNEC_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                    class="btn-executive btn-executive-secondary" data-tooltip="Bidang NEC">
                                                                    <i class="fas fa-bullseye me-2"></i>
                                                                    <span>NEC</span>
                                                                </a>
                                                                <?php endif; ?>

                                                                <!-- Maklumat Program Button -->
                                                                <a type="button" class="btn-executive btn-executive-secondary" data-bs-toggle="modal"
                                                                    data-bs-target="#maklumatProgram__<?php echo e($PROGRAM->kod_Program); ?>" data-tooltip="Maklumat Program">
                                                                    <i class="fas fa-info-circle me-2"></i>
                                                                    <span>Info</span>
                                                                </a>
                                                            </div>
                                                        </div>








                                                        <ul class="list-unstyled mb-0 flex-wrap">
                                                           
                                                           
                                                            <?php if(session()->get('jenprog') == 'spm'): ?>
                                                              
                                                                <div class="modal fade kelayakanMinimum_Modal"
                                                                    id="subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                    tabindex="-1" role="dialog"
                                                                    aria-labelledby="syarat-program-title"
                                                                    aria-hidden="true">
                                                                    <div class="modal-dialog modal-dialog-centered modal-llgg"
                                                                        role="document">
                                                                        <div
                                                                            class="modal-content rounded shadow-lg border-0 overflow-hidden">
                                                                            <button type="button"
                                                                                class="close float-right mr-2"
                                                                                data-dismiss="modal" aria-label="Close"
                                                                                style="display:flex; justify-content:flex-end; width:100%; padding:0.9rem; margin-top: -0.8rem;">
                                                                                <span aria-hidden="true">&times;</span>
                                                                            </button>
                                                                            <div class="modal-body" style="height: 90vh; overflow-y: auto;">
                                                                                <div class="text-left">
                                                                                    <h4 class="text-center"><b>Syarat
                                                                                            Program</b>
                                                                                    </h4>
                                                                                    <div class="container mt-100 mt-60">
                                                                                        <div class="row">
                                                                                            <div class="col-12">
                                                                                                <ul class="nav nav-pills shadow flex-column flex-sm-row mb-0 p-1 bg-white rounded overflow-hidden"
                                                                                                    id="pills-tab"
                                                                                                    role="tablist">
                                                                                                    <!--Syarat Am Tab-->
                                                                                                    <li
                                                                                                        class="nav-item col-sm-6">
                                                                                                        <a class="nav-link py-2 active rounded"
                                                                                                            id="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            data-toggle="pill"
                                                                                                            href="#syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            role="tab"
                                                                                                            aria-controls="syarat-am"
                                                                                                            aria-selected="false">
                                                                                                            <div
                                                                                                                class="text-center">
                                                                                                                <h6
                                                                                                                    class="mb-0">
                                                                                                                    Syarat Am</h6>
                                                                                                            </div>
                                                                                                        </a>
                                                                                                    </li>

                                                                                                    <!--Syarat Khas Tab-->
                                                                                                    <li
                                                                                                        class="nav-item col-sm-6">
                                                                                                        <a class="nav-link py-2 rounded"
                                                                                                            id="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            data-toggle="pill"
                                                                                                            href="#syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            role="tab"
                                                                                                            aria-controls="syarat-Khas"
                                                                                                            aria-selected="false">
                                                                                                            <div
                                                                                                                class="text-center">
                                                                                                                <h6
                                                                                                                    class="mb-0">
                                                                                                                    Syarat Khas
                                                                                                                </h6>
                                                                                                            </div>
                                                                                                        </a>
                                                                                                    </li>
                                                                                                </ul>

                                                                                                <div class="tab-content"
                                                                                                    id="pills-tabContent"
                                                                                                    style="padding-top: 2rem!important">
                                                                                                    <!--Paparan Syarat Am Tab-->
                                                                                                    <div class="card border-0 tab-pane fade show active"
                                                                                                        id="syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tabpanel"
                                                                                                        aria-labelledby="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>">

                                                                                                        <?php echo $__env->make('programPengajian.syarat_am_spm', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                        

                                                                                                    </div>

                                                                                                    <!--Paparan Syarat khas Tab-->
                                                                                                    <div class="card border-0 tab-pane fade"
                                                                                                        id="syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tabpanel"
                                                                                                        aria-labelledby="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>">
                                                                                                        <div class="text-muted contentLoad"
                                                                                                            style="font-weight: bold">
                                                                                                            <div
                                                                                                                align="center">
                                                                                                                <div
                                                                                                                    class="loader-spinner text-center">
                                                                                                                </div>
                                                                                                                <h4>Sila
                                                                                                                    tunggu
                                                                                                                    sebentar...
                                                                                                                </h4>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>



                                                            <?php elseif(session()->get('jenprog') == 'stpm'): ?>



                                                           

                                                            <div class="modal fade kelayakanMinimum_Modal"
                                                                id="subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                tabindex="-1" role="dialog"
                                                                aria-labelledby="syarat-program-title"
                                                                aria-hidden="true">
                                                                <div class="modal-dialog modal-dialog-centered modal-llgg"
                                                                    role="document" >
                                                                    <div
                                                                        class="modal-content rounded shadow-lg border-0 overflow-hidden">
                                                                        <button type="button"
                                                                            class="close float-right mr-2"
                                                                            data-dismiss="modal" aria-label="Close"
                                                                            style="display:flex; justify-content:flex-end; width:100%; padding:0.9rem; margin-top: -0.8rem;">
                                                                            <span aria-hidden="true">&times;</span>
                                                                        </button>
                                                                        <div class="modal-body" style="height: 90vh; overflow-y: auto;">
                                                                            <div class="text-left">
                                                                                <h4 class="text-center"><b>Syarat
                                                                                        Program</b>
                                                                                </h4>
                                                                                <div class="container mt-100 mt-60">
                                                                                    <div class="row">
                                                                                        <div class="col-12">
                                                                                            <ul class="nav nav-pills shadow flex-column flex-sm-row mb-0 p-1 bg-white rounded overflow-hidden"
                                                                                                id="pills-tab"
                                                                                                role="tablist">
                                                                                                <!--Syarat Am Tab-->
                                                                                                <li <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?> class="nav-item col-4" <?php else: ?> class="nav-item col-6" <?php endif; ?>>
                                                                                                    <a class="nav-link py-2 active rounded"
                                                                                                        id="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-am"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Am</h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>

                                                                                                <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?>
                                                                                                <li <?php echo e($PROGRAM->kod_Program == 'UM6143001' ? 'hidden' : null); ?>

                                                                                                    class="nav-item col-4">
                                                                                                    <a class="nav-link py-2 rounded"
                                                                                                        id="syarat-pendidikan-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-pendidikan-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-pendidikan"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Pendidikan</h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>
                                                                                                <?php endif; ?>


                                                                                                <!--Syarat Khas Tab-->
                                                                                                <li <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?> class="nav-item col-4" <?php else: ?> class="nav-item col-6" <?php endif; ?>>
                                                                                                    <a class="nav-link py-2 rounded"
                                                                                                        id="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-Khas"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Khas
                                                                                                            </h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>
                                                                                            </ul>

                                                                                            <div class="tab-content"
                                                                                                id="pills-tabContent"
                                                                                                style="padding-top: 2rem!important">
                                                                                                <!--Paparan Syarat Am Tab-->
                                                                                                <div class="card border-0 tab-pane fade show active"
                                                                                                    id="syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>">

                                                                                                    <?php if($PROGRAM->kategori_Pengajian=='G'): ?>
																										<?php echo $__env->make('programPengajian.syarat_am_diploma_g', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php elseif($PROGRAM->kategori_Pengajian=='E'): ?>
																										<?php echo $__env->make('programPengajian.syarat_am_diploma_e', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php elseif($PROGRAM->kategori_Pengajian=='F'): ?>
																										<?php echo $__env->make('programPengajian.syarat_am_diploma_f', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php else: ?>
																										<?php echo $__env->make('programPengajian.syarat_am_stpm', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php endif; ?>

                                                                                                </div>

                                                                                                <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?>
                                                                                                <div class="card border-0 tab-pane fade"
                                                                                                    id="syarat-pendidikan-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-pendidikan-tab-<?php echo e($PROGRAM->kod_Program); ?>">

                                                                                                    <?php echo $__env->make('programPengajian.syarat_am_pendidikan', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                </div>
                                                                                                <?php endif; ?>


                                                                                                <!--Paparan Syarat khas Tab-->
                                                                                                <div class="card border-0 tab-pane fade"
                                                                                                    id="syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>">
                                                                                                    <div class="text-muted contentLoad"
                                                                                                        style="font-weight: bold">
                                                                                                        <div
                                                                                                            align="center">
                                                                                                            <div
                                                                                                                class="loader-spinner text-center">
                                                                                                            </div>
                                                                                                            <h4>Sila
                                                                                                                tunggu
                                                                                                                sebentar...
                                                                                                            </h4>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>





                                                                
                                                            <?php endif; ?>
                                                            

                                                            
                                                            


                                                        </ul>
                                                    </div>
                                                </div>

                                                <!--end col-->
                                            </div>
                                            <!--end row-->
                                        </div>
                                        <!--end blog post-->
                                    </div>
                                    <?php echo $__env->make('programPengajian.modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <?php echo $__env->make('pageLock.tiadaMaklumat', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            <?php endif; ?>

                            <!-- PAGINATION START -->
                            <div class="col-12 mt-4 pt-2">
                                <?php echo $SENARAI_PROGRAM->links('programPengajian.list-paginator'); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>
    <script src="<?php echo e(asset('/assets/js/range-slider.js')); ?>"></script>

    <!-- Executive Modern Styling -->
    <style>
        /* Executive Card Styling with Premium Background */
        .executive-card {
            background:
                linear-gradient(135deg, rgba(44, 62, 80, 0.05) 0%, rgba(52, 152, 219, 0.05) 100%),
                linear-gradient(45deg, #f8f9fa 0%, #ffffff 50%, #f1f3f4 100%);
            border-radius: 16px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.08),
                0 2px 8px rgba(0, 0, 0, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(20px);
            overflow: hidden;
            position: relative;
        }

        .executive-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(44, 62, 80, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(52, 152, 219, 0.03) 0%, transparent 50%),
                linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            pointer-events: none;
        }

        .executive-card:hover {
            transform: translateY(-15px) scale(1.03);
            box-shadow:
                0 30px 80px rgba(0, 0, 0, 0.15),
                0 12px 30px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
        }

        /* Executive Clickable Card Effects */
        .executive-clickable-card {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .executive-clickable-card:hover {
            cursor: pointer;
        }

        .executive-clickable-card:hover .executive-info-card {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .executive-clickable-card:hover .btn-executive {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .executive-clickable-card:hover .executive-label {
            animation-duration: 1s;
            transform: scale(1.05);
        }

        .executive-card-body {
            background:
                linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.8) 100%);
            border-radius: 0 16px 16px 0;
            position: relative;
            backdrop-filter: blur(10px);
        }

        .executive-card-body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg,
                #2c3e50 0%,
                #34495e 25%,
                #3498db 50%,
                #2980b9 75%,
                #27ae60 100%);
            border-radius: 0 16px 0 0;
        }

        /* Executive Button Styling - Compact Single Row */
        .btn-executive {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 6px;
            text-decoration: none !important;
            font-weight: 600;
            font-size: 10px;
            border: 1px solid;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow:
                0 2px 8px rgba(0, 0, 0, 0.06),
                0 1px 3px rgba(0, 0, 0, 0.04);
            min-width: 65px;
            height: 32px;
            justify-content: center;
            letter-spacing: 0.3px;
            text-transform: uppercase;
            margin: 0 1px;
            flex: 1;
            max-width: 85px;
        }

        .btn-executive:hover {
            transform: translateY(-2px);
            text-decoration: none !important;
        }

        .btn-executive:focus {
            text-decoration: none !important;
            outline: none;
        }

        .executive-buttons-container {
            padding: 20px;
            background:
                linear-gradient(135deg, rgba(248, 249, 250, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%);
            border-radius: 10px;
            margin: 15px 0;
            border: 1px solid rgba(0, 0, 0, 0.04);
            box-shadow:
                0 3px 12px rgba(0, 0, 0, 0.04),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
        }

        /* Executive Header Section */
        .executive-header-section {
            margin-bottom: 20px;
        }

        .executive-title-container {
            margin-bottom: 15px;
        }

        .executive-program-title {
            font-size: 18px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
            line-height: 1.3;
            letter-spacing: -0.2px;
        }

        .executive-institution-name {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 0;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .executive-interview-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border-radius: 50%;
            font-size: 12px;
            margin-left: 8px;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
        }

        /* Executive Labels */
        .executive-labels-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }

        .executive-label {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .executive-label-feeder {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            color: white;
            animation: marqueeGlow 3s ease-in-out infinite;
        }

        .executive-label-stem {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            animation: marqueeGlow 3s ease-in-out infinite 1.5s;
        }

        .executive-label-content {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .executive-label-text {
            white-space: nowrap;
        }

        .executive-label-icon {
            font-size: 10px;
            opacity: 0.8;
        }

        @keyframes  marqueeGlow {
            0%, 100% {
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
                transform: scale(1.05);
            }
        }

        /* Executive Info Cards */
        .executive-info-cards {
            display: flex;
            gap: 12px;
            margin: 15px 0;
            flex-wrap: wrap;
        }

        .executive-info-card {
            flex: 1;
            min-width: 120px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 1px solid rgba(0, 0, 0, 0.06);
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;
        }

        .executive-info-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        }

        .executive-info-card-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            margin-bottom: 8px;
            color: #7f8c8d;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .executive-info-card-header i {
            font-size: 12px;
        }

        .executive-info-card-value {
            font-size: 14px;
            font-weight: 700;
            color: #2c3e50;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
        }

        .executive-info-tooltip {
            color: #3498db;
            font-size: 12px;
            cursor: help;
            transition: all 0.3s ease;
        }

        .executive-info-tooltip:hover {
            color: #2980b9;
            transform: scale(1.2);
        }

        /* Executive Modern Tooltip Styling */
        .tooltip {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .tooltip .tooltip-inner {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 12px;
            font-weight: 500;
            line-height: 1.4;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            max-width: 300px;
            text-align: left;
        }

        .tooltip .tooltip-arrow {
            display: none;
        }

        .tooltip.show {
            opacity: 1;
            animation: tooltipFadeIn 0.3s ease-out;
        }

        @keyframes  tooltipFadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Executive Tooltip for Labels */
        .executive-label[data-toggle="tooltip"] {
            position: relative;
        }

        .executive-label[data-toggle="tooltip"]:hover::after {
            content: attr(title);
            position: absolute;
            bottom: 120%;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 11px;
            white-space: nowrap;
            z-index: 1000;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            animation: tooltipFadeIn 0.3s ease-out;
        }

        /* Executive Info Section Styling */
        .executive-info-section {
            width: 100%;
            display: flex;
            justify-content: flex-end;
            margin-top: 20px !important;
        }

        .executive-info-container {
            position: relative;
        }

        .executive-info-link {
            display: inline-flex;
            align-items: center;
            text-decoration: none !important;
            color: #2c3e50;
            font-weight: 600;
            font-size: 14px;
            letter-spacing: 0.5px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            padding: 12px 20px;
            border-radius: 8px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 249, 250, 0.9) 100%);
            border: 1px solid rgba(44, 62, 80, 0.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            position: relative;
            overflow: hidden;
        }

        .executive-info-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(44, 62, 80, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .executive-info-link:hover::before {
            left: 100%;
        }

        .executive-info-link:hover {
            color: #1a252f !important;
            text-decoration: none !important;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 1) 100%);
        }

        .executive-info-content {
            display: flex;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .executive-info-text {
            margin-right: 8px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            font-size: 12px;
        }

        .executive-info-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        .executive-info-link:hover .executive-info-icon {
            transform: translateX(3px) scale(1.1);
            background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
        }

        .executive-info-underline {
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #2c3e50 0%, #3498db 100%);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .executive-info-link:hover .executive-info-underline {
            width: 100%;
        }

        .btn-compact:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            text-decoration: none !important;
        }

        .btn-compact:active {
            transform: translateY(0) scale(0.98);
            transition: transform 0.1s;
        }

        .btn-compact:focus {
            text-decoration: none !important;
            outline: none;
        }

        /* Button Icon */
        .btn-compact-icon {
            margin-right: 6px;
            font-size: 14px;
            transition: transform 0.3s ease;
        }

        .btn-compact:hover .btn-compact-icon {
            transform: scale(1.1) rotate(5deg);
        }

        /* Button Text */
        .btn-compact-text {
            font-weight: 600;
            letter-spacing: 0.3px;
            white-space: nowrap;
        }

        /* Executive Button Color Variants */
        .btn-executive-primary {
            border-color: #1a252f;
            color: #1a252f;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .btn-executive-primary:hover {
            background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
            color: white !important;
            border-color: #1a252f;
            text-decoration: none !important;
            box-shadow: 0 6px 20px rgba(26, 37, 47, 0.3);
        }

        .btn-executive-success {
            border-color: #1e8449;
            color: #1e8449;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .btn-executive-success:hover {
            background: linear-gradient(135deg, #1e8449 0%, #27ae60 100%);
            color: white !important;
            border-color: #1e8449;
            text-decoration: none !important;
            box-shadow: 0 6px 20px rgba(30, 132, 73, 0.3);
        }

        .btn-executive-info {
            border-color: #2874a6;
            color: #2874a6;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .btn-executive-info:hover {
            background: linear-gradient(135deg, #2874a6 0%, #3498db 100%);
            color: white !important;
            border-color: #2874a6;
            text-decoration: none !important;
            box-shadow: 0 6px 20px rgba(40, 116, 166, 0.3);
        }

        .btn-executive-warning {
            border-color: #d68910;
            color: #d68910;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .btn-executive-warning:hover {
            background: linear-gradient(135deg, #d68910 0%, #f39c12 100%);
            color: white !important;
            border-color: #d68910;
            text-decoration: none !important;
            box-shadow: 0 6px 20px rgba(214, 137, 16, 0.3);
        }

        .btn-executive-secondary {
            border-color: #566573;
            color: #566573;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .btn-executive-secondary:hover {
            background: linear-gradient(135deg, #566573 0%, #95a5a6 100%);
            color: white !important;
            border-color: #566573;
            text-decoration: none !important;
            box-shadow: 0 6px 20px rgba(86, 101, 115, 0.3);
        }

        /* Simplified Tooltip Styling */
        .btn-compact[data-tooltip] {
            position: relative;
        }

        .btn-compact[data-tooltip]:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 120%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        @keyframes  fadeIn {
            from { opacity: 0; transform: translateX(-50%) translateY(10px); }
            to { opacity: 1; transform: translateX(-50%) translateY(0); }
        }

        /* Action Buttons Container */
        .action-buttons-container {
            margin: 0;
        }

        /* Executive Responsive Design */
        @media (max-width: 768px) {
            .btn-executive {
                padding: 5px 8px;
                font-size: 9px;
                min-width: 55px;
                height: 28px;
                margin: 0;
                max-width: 70px;
            }

            .executive-buttons-container {
                padding: 16px;
                margin: 12px 0;
                border-radius: 8px;
            }

            .executive-card {
                border-radius: 12px;
            }

            .executive-card-body {
                border-radius: 0 12px 12px 0;
            }

            .executive-program-title {
                font-size: 16px;
            }

            .executive-info-cards {
                gap: 8px;
            }

            .executive-info-card {
                min-width: 100px;
                padding: 10px;
            }

            .executive-labels-container {
                gap: 8px;
            }

            .executive-label {
                padding: 4px 8px;
                font-size: 10px;
            }
        }

        @media (max-width: 480px) {
            .btn-executive {
                min-width: 45px;
                height: 26px;
                font-size: 8px;
                padding: 4px 6px;
                margin: 0;
                max-width: 60px;
            }

            .executive-buttons-container {
                padding: 12px;
                margin: 10px 0;
            }

            .executive-program-title {
                font-size: 15px;
            }

            .executive-info-cards {
                flex-direction: column;
                gap: 6px;
            }

            .executive-info-card {
                min-width: auto;
                padding: 8px;
            }

            .executive-label {
                padding: 3px 6px;
                font-size: 9px;
            }
        }

        /* Executive Background Pattern Enhancement */
        .executive-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(44, 62, 80, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(52, 152, 219, 0.02) 0%, transparent 50%);
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .executive-card:hover::after {
            opacity: 1;
        }

        /* Executive Subtle Animation */
        .executive-card {
            animation: subtleFloat 6s ease-in-out infinite;
        }

        @keyframes  subtleFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-2px); }
        }

        .executive-card:hover {
            animation: none;
        }

        /* Executive Animation Effects */
        .executive-card {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes  fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .btn-executive {
            animation: slideInRight 0.5s ease-out;
        }

        @keyframes  slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Executive Tooltip Enhancement */
        .btn-executive[data-tooltip]:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 120%;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 11px;
            white-space: nowrap;
            z-index: 1000;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            animation: tooltipFadeIn 0.3s ease;
        }

        @keyframes  tooltipFadeIn {
            from { opacity: 0; transform: translateX(-50%) translateY(10px); }
            to { opacity: 1; transform: translateX(-50%) translateY(0); }
        }

        /* Executive Focus States */
        .btn-executive:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(44, 62, 80, 0.25);
        }

        .btn-executive-success:focus {
            box-shadow: 0 0 0 3px rgba(30, 132, 73, 0.25);
        }

        .btn-executive-info:focus {
            box-shadow: 0 0 0 3px rgba(40, 116, 166, 0.25);
        }

        .btn-executive-warning:focus {
            box-shadow: 0 0 0 3px rgba(214, 137, 16, 0.25);
        }

        .btn-executive-secondary:focus {
            box-shadow: 0 0 0 3px rgba(86, 101, 115, 0.25);
        }

        /* Remove any unwanted effects */
        .btn-executive,
        .btn-executive:hover,
        .btn-executive:focus,
        .btn-executive:active,
        .btn-executive:visited {
            text-decoration: none !important;
            border-bottom: none !important;
        }

        .btn-executive::before,
        .btn-executive::after {
            display: none;
        }

        /* Loading State */
        .btn-compact.loading {
            pointer-events: none;
            opacity: 0.7;
        }

        .btn-compact.loading .btn-compact-icon {
            animation: spin 1s linear infinite;
        }

        @keyframes  spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Focus States for Accessibility */
        .btn-compact:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
        }

        .btn-compact-success:focus {
            box-shadow: 0 0 0 3px rgba(25, 135, 84, 0.25);
        }

        .btn-compact-info:focus {
            box-shadow: 0 0 0 3px rgba(13, 202, 240, 0.25);
        }

        .btn-compact-warning:focus {
            box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.25);
        }

        .btn-compact-secondary:focus {
            box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.25);
        }

        /* Remove any pseudo-elements that might cause black bars */
        .btn-compact::before {
            display: none;
        }

        .btn-compact::after {
            display: none;
        }

        /* Ensure no underlines or text decorations */
        .btn-compact,
        .btn-compact:hover,
        .btn-compact:focus,
        .btn-compact:active,
        .btn-compact:visited {
            text-decoration: none !important;
            border-bottom: none !important;
        }
    </style>

    <!-- Executive React-like Interactive JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Executive Component State Management
            const ExecutiveCardComponent = {
                state: {
                    isLoading: false,
                    activeButton: null,
                    cardAnimated: false
                },

                init() {
                    this.bindEvents();
                    this.initAnimations();
                    this.setupIntersectionObserver();
                },

                bindEvents() {
                    // Executive button interactions
                    const executiveButtons = document.querySelectorAll('.btn-executive');
                    const executiveCards = document.querySelectorAll('.executive-card');
                    const executiveLabels = document.querySelectorAll('.executive-label');
                    const executiveInfoCards = document.querySelectorAll('.executive-info-card');
                    const clickableCards = document.querySelectorAll('.executive-clickable-card');

                    // Enhanced button interactions with React-like state
                    executiveButtons.forEach((button, index) => {
                        button.addEventListener('click', (e) => this.handleButtonClick(e, button, index));
                        button.addEventListener('mouseenter', (e) => this.handleButtonHover(e, button));
                        button.addEventListener('mouseleave', (e) => this.handleButtonLeave(e, button));
                    });

                    // Card interactions
                    executiveCards.forEach((card, index) => {
                        card.addEventListener('mouseenter', (e) => this.handleCardHover(e, card));
                        card.addEventListener('mouseleave', (e) => this.handleCardLeave(e, card));
                    });

                    // Clickable card interactions
                    clickableCards.forEach((card, index) => {
                        card.addEventListener('click', (e) => this.handleCardClick(e, card));
                    });

                    // Label marquee effects
                    executiveLabels.forEach((label, index) => {
                        this.initLabelMarquee(label, index);
                    });

                    // Info card interactions
                    executiveInfoCards.forEach((card, index) => {
                        card.addEventListener('mouseenter', (e) => this.handleInfoCardHover(e, card));
                        card.addEventListener('mouseleave', (e) => this.handleInfoCardLeave(e, card));
                    });

                    // Initialize modern tooltips
                    this.initModernTooltips();
                },

                handleButtonClick(e, button, index) {
                    // Prevent card modal from opening when button is clicked
                    e.stopPropagation();

                    if (this.state.isLoading) return;

                    this.state.isLoading = true;
                    this.state.activeButton = button;

                    const icon = button.querySelector('i');
                    const originalIcon = icon.className;

                    // Add loading state with React-like state management
                    button.classList.add('loading');
                    icon.className = 'fas fa-spinner fa-spin';

                    // Create ripple effect
                    this.createRippleEffect(button);

                    // Simulate async operation
                    setTimeout(() => {
                        this.state.isLoading = false;
                        this.state.activeButton = null;
                        button.classList.remove('loading');
                        icon.className = originalIcon;
                    }, 1000);
                },

                handleCardClick(e, card) {
                    // Only trigger if not clicking on a button or interactive element
                    if (e.target.closest('.btn-executive') ||
                        e.target.closest('.executive-info-tooltip') ||
                        e.target.closest('[data-toggle="tooltip"]')) {
                        return;
                    }

                    // Add click effect
                    card.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        card.style.transform = '';
                    }, 150);
                },

                handleButtonHover(e, button) {
                    if (this.state.isLoading && this.state.activeButton === button) return;

                    button.style.transform = 'translateY(-3px) scale(1.05)';
                    button.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
                },

                handleButtonLeave(e, button) {
                    if (this.state.isLoading && this.state.activeButton === button) return;

                    button.style.transform = 'translateY(0) scale(1)';
                    button.style.boxShadow = '0 3px 12px rgba(0, 0, 0, 0.08)';
                },

                handleCardHover(e, card) {
                    card.style.transform = 'translateY(-12px) scale(1.02)';
                    card.style.boxShadow = '0 25px 70px rgba(0, 0, 0, 0.15)';

                    // Enhance child elements
                    const labels = card.querySelectorAll('.executive-label');
                    labels.forEach(label => {
                        label.style.animationDuration = '1s';
                    });
                },

                handleCardLeave(e, card) {
                    card.style.transform = 'translateY(0) scale(1)';
                    card.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1)';

                    // Reset child elements
                    const labels = card.querySelectorAll('.executive-label');
                    labels.forEach(label => {
                        label.style.animationDuration = '3s';
                    });
                },

                handleInfoCardHover(e, card) {
                    card.style.transform = 'translateY(-4px) scale(1.05)';
                    card.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.12)';
                },

                handleInfoCardLeave(e, card) {
                    card.style.transform = 'translateY(0) scale(1)';
                    card.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.04)';
                },

                createRippleEffect(button) {
                    const ripple = document.createElement('span');
                    ripple.classList.add('ripple-effect');

                    const rect = button.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = (rect.width / 2 - size / 2) + 'px';
                    ripple.style.top = (rect.height / 2 - size / 2) + 'px';

                    button.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                },

                initLabelMarquee(label, index) {
                    // Enhanced marquee effect for labels
                    const delay = index * 500;

                    setTimeout(() => {
                        label.style.animation = `marqueeGlow 3s ease-in-out infinite ${delay}ms`;
                    }, delay);
                },

                initAnimations() {
                    // Staggered entrance animations
                    const buttons = document.querySelectorAll('.btn-executive');
                    const infoCards = document.querySelectorAll('.executive-info-card');
                    const labels = document.querySelectorAll('.executive-label');

                    // Animate buttons
                    buttons.forEach((button, index) => {
                        button.style.opacity = '0';
                        button.style.transform = 'translateX(30px)';

                        setTimeout(() => {
                            button.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                            button.style.opacity = '1';
                            button.style.transform = 'translateX(0)';
                        }, index * 100 + 200);
                    });

                    // Animate info cards
                    infoCards.forEach((card, index) => {
                        card.style.opacity = '0';
                        card.style.transform = 'translateY(20px)';

                        setTimeout(() => {
                            card.style.transition = 'all 0.5s ease-out';
                            card.style.opacity = '1';
                            card.style.transform = 'translateY(0)';
                        }, index * 150 + 100);
                    });

                    // Animate labels
                    labels.forEach((label, index) => {
                        label.style.opacity = '0';
                        label.style.transform = 'scale(0.8)';

                        setTimeout(() => {
                            label.style.transition = 'all 0.4s ease-out';
                            label.style.opacity = '1';
                            label.style.transform = 'scale(1)';
                        }, index * 200 + 300);
                    });
                },

                setupIntersectionObserver() {
                    const observer = new IntersectionObserver((entries) => {
                        entries.forEach(entry => {
                            if (entry.isIntersecting && !this.state.cardAnimated) {
                                this.state.cardAnimated = true;
                                entry.target.style.animation = 'fadeInUp 0.8s ease-out both';

                                // Trigger child animations
                                setTimeout(() => {
                                    this.initAnimations();
                                }, 200);
                            }
                        });
                    }, {
                        threshold: 0.1,
                        rootMargin: '0px 0px -50px 0px'
                    });

                    document.querySelectorAll('.executive-card').forEach(card => {
                        observer.observe(card);
                    });
                },

                initModernTooltips() {
                    // Initialize Bootstrap tooltips with custom styling
                    if (typeof $ !== 'undefined' && $.fn.tooltip) {
                        $('[data-toggle="tooltip"]').tooltip({
                            template: '<div class="tooltip executive-tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',
                            animation: true,
                            delay: { show: 300, hide: 100 },
                            html: true
                        });
                    }

                    // Custom tooltip behavior for executive elements
                    document.querySelectorAll('.executive-info-tooltip').forEach(tooltip => {
                        tooltip.addEventListener('mouseenter', function() {
                            this.style.transform = 'scale(1.2)';
                            this.style.color = '#2980b9';
                        });

                        tooltip.addEventListener('mouseleave', function() {
                            this.style.transform = 'scale(1)';
                            this.style.color = '#3498db';
                        });
                    });

                    // Enhanced label tooltips
                    document.querySelectorAll('.executive-label').forEach(label => {
                        if (label.hasAttribute('data-toggle')) {
                            label.addEventListener('mouseenter', function() {
                                this.style.transform = 'scale(1.05)';
                                this.style.zIndex = '1000';
                            });

                            label.addEventListener('mouseleave', function() {
                                this.style.transform = 'scale(1)';
                                this.style.zIndex = '';
                            });
                        }
                    });
                }
            };

            // Initialize the Executive Card Component
            ExecutiveCardComponent.init();

            // Add dynamic ripple effect styles
            const rippleStyle = document.createElement('style');
            rippleStyle.textContent = `
                .ripple-effect {
                    position: absolute;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.6);
                    transform: scale(0);
                    animation: ripple 0.6s linear;
                    pointer-events: none;
                }

                @keyframes  ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }

                .btn-executive.loading {
                    pointer-events: none;
                    opacity: 0.8;
                }

                /* Executive gradient text effect */
                .executive-gradient-text {
                    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                }

                /* Executive glow effect on focus */
                .btn-executive:focus {
                    animation: executiveGlow 2s ease-in-out infinite alternate;
                }

                @keyframes  executiveGlow {
                    from {
                        box-shadow: 0 0 5px rgba(44, 62, 80, 0.5);
                    }
                    to {
                        box-shadow: 0 0 20px rgba(44, 62, 80, 0.8);
                    }
                }
            `;
            document.head.appendChild(rippleStyle);

            // Executive theme color cycling (subtle)
            let colorIndex = 0;
            const colors = ['#2c3e50', '#27ae60', '#3498db', '#f39c12', '#95a5a6'];

            setInterval(() => {
                const cardBodies = document.querySelectorAll('.executive-card-body::before');
                colorIndex = (colorIndex + 1) % colors.length;
            }, 10000); // Change every 10 seconds
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/index.blade.php ENDPATH**/ ?>