<?php $__env->startSection('content'); ?>
    <section class="bg-half bg-light d-table w-100">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-12 text-center">
                    <div class="page-next-level">
                        <h3 class="title"> PROGRAM PENGAJIAN</h3>
                        <div class="page-next">
                            <nav aria-label="breadcrumb" class="d-inline-block">
                                <ul class="breadcrumb bg-white rounded shadow mb-0">
                                    <li class="breadcrumb-item"><a href="<?php echo e(url('/')); ?>"><PERSON><PERSON></a></li>
                                    <li class="breadcrumb-item"><a
                                            href="<?php echo e(url('kategoriCalon', [session('jenprog')])); ?>">Kategori
                                            <?php echo e(Request::route('kodkatag')); ?></a></li>
                                    <li class="breadcrumb-item"><a href="#">Program Pengajian</a></li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="position-relative">
        <div class="shape overflow-hidden text-white">
            <svg viewBox="0 0 2880 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 48H1437.5H2880V0H2160C1442.5 52 720 0 720 0H0V48Z" fill="currentColor"></path>
            </svg>
        </div>
    </div>

    <section class="section">
        <div class="container">
            <form action="<?php echo e(url('ProgramPengajian/kategoriCalon/' . Request::route('kodkatag'))); ?>" method="post">
                <?php echo csrf_field(); ?>
                <div class="row">
                    <div class="col-lg-3 col-md-4 col-12">
                        <div class="card border-0 sidebar sticky-bar">
                            <div class="card-body p-0">
                                <!-- SEARCH -->
                                <div class="widget">
                                    <div id="search2" class="widget-search mb-0">
                                        <form class="searchform">
                                            <div>
                                                <input type="text" class="border rounded" id="fuzzySearch"
                                                    name="fuzzySearch" placeholder="Carian Program..."
                                                    value="<?php echo e(old('fuzzySearch')); ?>">
                                            </div>
                                        </form>
                                    </div>
                                </div>

                                <div class="widget mt-4 pt-2">
                                    <div class="row" style="margin-bottom: -1.3rem">
                                        <div class="column"
                                            style="float: left;
                                    width: 50%;
                                    margin-top: 6px; margin-left: 16px; margin-right: -25px;">
                                            <h5 class="widget-title">Tapisan</h5>
                                        </div>
                                        <div class="uncheck-clear-button" style="text-align: right">
                                            <button type="submit" class="badge badge-pill badge-success" id="searching" name="searching" style="border: 1px solid #000; padding-left: 12px; padding-right: 12px;;"><i class="fas fa-search"></i> Cari</button>
                                            <button type="submit" class="badge badge-pill badge-danger" id="clearFiltering" name="clearFiltering" style="border: 1px solid #000; padding-left: 9px; padding-right: 9px;"><i class="fas fa-trash-alt"></i> Clear</button>
                                        </div>
                                    </div>

                                    <ul class="list-unstyled mt-4 mb-0 blog-categories sidebar-menu">
                                        <li class="have-children"><a href="#"><span class="fa fa-th-list"
                                                    style="padding-right: 7px;"></span>Bidang</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" name="pBidang[]" value="00"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('00', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck1">
                                                                <label class="custom-control-label"
                                                                    for="customCheck1">Program Dan Kelayakan Generik</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="01" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('01', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck2">
                                                                <label class="custom-control-label"
                                                                    for="customCheck2">Pendidikan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="02" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('02', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck4">
                                                                <label class="custom-control-label"
                                                                    for="customCheck4">Sastera
                                                                    Dan Kemanusiaan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="03" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('03', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck5">
                                                                <label class="custom-control-label"
                                                                    for="customCheck5">Sains
                                                                    Sosial, Kewartawanan Dan Maklumat</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="04" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('04', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck6">
                                                                <label class="custom-control-label"
                                                                    for="customCheck6">Perniagaan, Pentadbiran Dan
                                                                    Perundangan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="05" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('05', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck7">
                                                                <label class="custom-control-label"
                                                                    for="customCheck7">Sains Semulajadi, Matematik Dan
                                                                    Statistik</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="06" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('06', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck8">
                                                                <label class="custom-control-label"
                                                                    for="customCheck8">Teknologi Maklumat Dan
                                                                    Komunikasi</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="07" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('07', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck9">
                                                                <label class="custom-control-label"
                                                                    for="customCheck9">Kejuruteraan, Pembuatan Dan
                                                                    Pembinaan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="08" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('08', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck10">
                                                                <label class="custom-control-label"
                                                                    for="customCheck10">Pertanian, Perhutanan, Perikanan
                                                                    Dan Vaterinar</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="09" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('09', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck11">
                                                                <label class="custom-control-label"
                                                                    for="customCheck11">Kesihatan Dan kebajikan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="10" name="pBidang[]"
                                                                    <?php if(!empty(session()->get('jBIDANG')) && in_array('10', session()->get('jBIDANG'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck12">
                                                                <label class="custom-control-label"
                                                                    for="customCheck12">Perkhidmatan</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <li class="have-children"><a href="#"><span class="fa fa-university"
                                                    style="padding-right: 7px;"></span>IPTA</a>
                                            <ul>
                                                <?php if(session()->get('jenprog') == 'spm'): ?>
                                                    
                                                    
                                                    
                                                    
                                                    
                                                    
                                                    
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="OM"
                                                                        name="carianIPTA[]"
                                                                        <?php if(!empty(session()->get('jIPTA')) && in_array('OM', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA30">
                                                                    <label class="custom-control-label" for="carianIPTA30">Kolej Mara</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="FC"
                                                                        name="carianIPTA[]"
                                                                        <?php if(!empty(session()->get('jIPTA')) && in_array('FC', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA2">
                                                                    <label class="custom-control-label" for="carianIPTA2">Kolej Komuniti</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="OP"
                                                                        name="carianIPTA[]"
                                                                        <?php if(!empty(session()->get('jIPTA')) && in_array('OP', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA31">
                                                                    <label class="custom-control-label" for="carianIPTA31">KPM</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="FB"
                                                                        name="carianIPTA[]"
                                                                        <?php if(!empty(session()->get('jIPTA')) && in_array('FB', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="carianIPTA1">
                                                                    <label class="custom-control-label"
                                                                        for="carianIPTA1">Politeknik</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                <?php endif; ?>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UY" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UY', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA27">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA27">UIAM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UE" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UE', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA13">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA13">UITM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UK" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UK', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA17">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA17">UKM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UM" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UM', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA19">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA19">UM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UJ" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UJ', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA16">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA16">UMPSA</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UL" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UL', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA18">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA18">UMK</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UH" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UH', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA15">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA15">UMS</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UG" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UG', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA14">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA14">UMT</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UR" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UR', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA22">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA22">UNIMAP</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UW" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UW', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA26">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA26">UNIMAS</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UD" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UD', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA12">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA12">UNISZA</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UP" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UP', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA20">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA20">UPM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UZ" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UZ', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA28">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA28">UPNM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UA" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UA', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA9">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA9">UPSI</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UQ" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UQ', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA21">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA21">USIM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="US" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('US', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA23">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA23">USM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UT" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UT', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA24">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA24">UTM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UC" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UC', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA11">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA11">UTeM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UB" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UB', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA10">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA10">UTHM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="UU" name="carianIPTA[]"
                                                                    <?php if(!empty(session()->get('jIPTA')) && in_array('UU', session()->get('jIPTA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="carianIPTA25">
                                                                <label class="custom-control-label"
                                                                    for="carianIPTA25">UUM</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <?php if(session()->get('jenprog') == 'spm'): ?>
                                            <li class="have-children"><a href="#"><span class="fa fa-code-fork"
                                                        style="padding-right: 7px;"></span>Peringkat Pengajian</a>
                                                <ul>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" name="peringkatPengajian[]"
                                                                        value="0"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('0', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck37">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck37">Asasi/Matrikulasi</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="1"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('1', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck38">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck38">Sijil-Kredit Graduan Min
                                                                        15</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="2"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('2', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck39">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck39">Sijil-Kredit Graduan Min
                                                                        30</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="3"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('3', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck40">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck40">Sijil-Kredit Graduan Min
                                                                        60</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="4"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('4', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck41">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck41">Diploma</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="5"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('5', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck42">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck42">Diploma Lanjutan</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="6"
                                                                        name="peringkatPengajian[]"
                                                                        <?php if(!empty(session()->get('jPERINGKATpengajian')) && in_array('6', session()->get('jPERINGKATpengajian'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck43">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck43">Sarjana Muda</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </li>
                                        <?php endif; ?>
                                        
                                        <li class="have-children"><a href="#"><span class="fa fa-cogs"
                                                    style="padding-right: 7px;"></span>Program TVET</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="Y" name="pTVET[]"
                                                                    <?php if(!empty(session()->get('jTVET')) && in_array('Y', session()->get('jTVET'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck33">
                                                                <label class="custom-control-label"
                                                                    for="customCheck33">Ya</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="T" name="pTVET[]"
                                                                    <?php if(!empty(session()->get('jTVET')) && in_array('T', session()->get('jTVET'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck34">
                                                                <label class="custom-control-label"
                                                                    for="customCheck34">Tidak</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <li class="have-children"><a href="#"><span class="fa fa-pie-chart"
                                                    style="padding-right: 7px;"></span>Mod Pengajian</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="Y"
                                                                    name="ModPengajian[]"
                                                                    <?php if(!empty(session()->get('jMODpengajian')) && in_array('Y', session()->get('jMODpengajian'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck35">
                                                                <label class="custom-control-label"
                                                                    for="customCheck35">2U2I/3U1I</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="T"
                                                                    name="ModPengajian[]"
                                                                    <?php if(!empty(session()->get('jMODpengajian')) && in_array('T', session()->get('jMODpengajian'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck36">
                                                                <label class="custom-control-label"
                                                                    for="customCheck36">Konvensional</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                        <?php if(session()->get('jenprog') == 'stpm'): ?>
                                            <li class="have-children"><a href="#"><span class="fa fa-certificate"
                                                        style="padding-right: 7px;"></span>Joint/Dual/Double Degree</a>
                                                <ul>
                                                    <li style="margin-bottom: -0.5rem;">
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="Y"
                                                                        name="pDoubleDegree[]"
                                                                        <?php if(!empty(session()->get('jDOUBLE_DEGREE')) && in_array('Y', session()->get('jDOUBLE_DEGREE'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck44">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck44">Ya</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                    <li>
                                                        <div class="form-check form-check-inline">
                                                            <div class="form-group mb-0">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" value="T"
                                                                        name="pDoubleDegree[]"
                                                                        <?php if(!empty(session()->get('jDOUBLE_DEGREE')) && in_array('T', session()->get('jDOUBLE_DEGREE'))): ?> checked <?php endif; ?>
                                                                        class="custom-control-input checkbox-filter"
                                                                        id="customCheck45">
                                                                    <label class="custom-control-label"
                                                                        for="customCheck45">Tidak</label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </li>
                                        <?php endif; ?>
                                        <li class="have-children"><a href="#"><span class="fa fa-hashtag"
                                                    style="padding-right: 7px;"></span>Bertemu duga / Ujian</a>
                                            <ul>
                                                <li style="margin-bottom: -0.5rem;">
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="Y" name="pTemuduga[]"
                                                                    <?php if(!empty(session()->get('jTEMUDUGA')) && in_array('Y', session()->get('jTEMUDUGA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck31">
                                                                <label class="custom-control-label"
                                                                    for="customCheck31">Ya</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                                <li>
                                                    <div class="form-check form-check-inline">
                                                        <div class="form-group mb-0">
                                                            <div class="custom-control custom-checkbox">
                                                                <input type="checkbox" value="T" name="pTemuduga[]"
                                                                    <?php if(!empty(session()->get('jTEMUDUGA')) && in_array('T', session()->get('jTEMUDUGA'))): ?> checked <?php endif; ?>
                                                                    class="custom-control-input checkbox-filter"
                                                                    id="customCheck32">
                                                                <label class="custom-control-label"
                                                                    for="customCheck32">Tidak</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>

										<?php if(substr(Request::route('kodkatag'),0,1)!='G' && substr(Request::route('kodkatag'),0,1)!='E' && substr(Request::route('kodkatag'),0,1)!='F'): ?>
                                        <li class="have-children"><a href="#"><span class="fa fa-line-chart"
                                                    style="padding-right: 7px;"></span>Minimum Purata Merit</a>
                                            <ul>
                                                <li>
                                                    <input type="range" min="0" max="100" value="0.00"
                                                        step="0.01" style="width: 60%" id="meritProgram"
                                                        name="meritProgram">
                                                    <b><label id="rangeDisplay" style="padding-left: 1rem"></label>%</b>
                                                </li>
                                            </ul>
                                        </li>
										<?php endif; ?>
                                        
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-9 col-md-8 col-12 mt-5 pt-2 mt-sm-0 pt-sm-0">
                        <div class="row align-items-center">
                            <div class="col-lg-8 col-md-7">
                                <div class="section-title">
                                    <h5 class="mb-0">Paparan
                                        <?php echo e($SENARAI_PROGRAM->firstItem()); ?> - <?php echo e($SENARAI_PROGRAM->lastItem()); ?> daripada
                                        <?php echo e($SENARAI_PROGRAM->total()); ?> carian</h5>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <?php if(count($SENARAI_PROGRAM) != '0'): ?>
                                <?php $__currentLoopData = $SENARAI_PROGRAM; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $PROGRAM): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="col-12 mt-4 pt-2">
                                        <!-- Modern Executive Card Component -->
                                        <div class="card executive-modern-card border-0 position-relative"
                                             data-bs-toggle="modal"
                                             data-bs-target="#maklumatProgram__<?php echo e($PROGRAM->kod_Program); ?>"
                                             onclick="handleCardClick(event, '<?php echo e($PROGRAM->kod_Program); ?>')"
                                             onmouseenter="handleCardHover(this, true)"
                                             onmouseleave="handleCardHover(this, false)"
                                             style="cursor: pointer;"
                                             role="button"
                                             tabindex="0">
                                            <div class="card-body p-4">
                                                <!-- Executive Header Section -->
                                                <div class="row align-items-center mb-4">
                                                    <div class="col-auto">
                                                        <div class="executive-logo-container">
                                                            <?php echo $__env->make('programPengajian.logoUA-ILKA', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                        </div>
                                                    </div>
                                                    <div class="col">
                                                        <div class="executive-program-header">
                                                            <h5 class="executive-program-title mb-2">
                                                                <?php echo e($PROGRAM->nama_Program); ?>

                                                                <?php if($PROGRAM->program_Temuduga == 'Y'): ?>
                                                                    <span class="badge bg-danger ms-2 executive-interview-badge">
                                                                        <i class="fas fa-user-tie me-1"></i>Interview
                                                                    </span>
                                                                <?php endif; ?>
                                                            </h5>

                                                            <p class="executive-university-name text-muted mb-3">
                                                                <i class="fas fa-university me-2"></i>
                                                                <?php echo e(Str::title($PROGRAM->nama_IPTA)); ?>

                                                            </p>

                                                            <!-- Executive Program Badges -->
                                                            <div class="executive-badges-container mb-3">
                                                                <?php if(session()->get('jenprog') == 'spm'): ?>
                                                                    <?php if($PROGRAM->program_FEEDER == 'Y'): ?>
                                                                        <span class="badge executive-badge-feeder rounded-pill me-2 mb-1"
                                                                              data-bs-toggle="tooltip"
                                                                              data-bs-placement="top"
                                                                              title="Program pembekal kepada program Ijazah Sarjana Muda di Universiti Awam berkenaan sahaja.">
                                                                            <i class="fas fa-seedling me-1"></i>Program Perintis (Feeder)
                                                                        </span>
                                                                    <?php endif; ?>
                                                                    <?php if($PROGRAM->program_STEM == 'Y'): ?>
                                                                        <span class="badge executive-badge-stem rounded-pill me-2 mb-1">
                                                                            <i class="fas fa-atom me-1"></i>STEM
                                                                        </span>
                                                                    <?php endif; ?>
                                                                    <?php if($PROGRAM->program_TVET == 'Y'): ?>
                                                                        <span class="badge executive-badge-tvet rounded-pill me-2 mb-1"
                                                                              data-bs-toggle="tooltip"
                                                                              data-bs-placement="top"
                                                                              title="Program pengajian yang berfokus kepada pembangunan bakat holistik iaitu memiliki pengetahuan teknikal, kemahiran dan insaniah.">
                                                                            <i class="fas fa-tools me-1"></i>TVET
                                                                        </span>
                                                                    <?php endif; ?>
                                                                <?php endif; ?>

                                                                <?php if(session()->get('jenprog') == 'stpm'): ?>
                                                                    <?php if($PROGRAM->program_KOMPETITIF == 'Y'): ?>
                                                                        <span class="badge executive-badge-competitive rounded-pill me-2 mb-1">
                                                                            <i class="fas fa-trophy me-1"></i>Kompetitif
                                                                        </span>
                                                                    <?php endif; ?>
                                                                    <?php if($PROGRAM->program_BTECH == 'Y'): ?>
                                                                        <span class="badge executive-badge-btech rounded-pill me-2 mb-1">
                                                                            <i class="fas fa-cogs me-1"></i>BTECH
                                                                        </span>
                                                                    <?php endif; ?>
                                                                    <?php if($PROGRAM->program_TVET == 'Y'): ?>
                                                                        <span class="badge executive-badge-tvet rounded-pill me-2 mb-1"
                                                                              data-bs-toggle="tooltip"
                                                                              data-bs-placement="top"
                                                                              title="Program pengajian yang berfokus kepada pembangunan bakat holistik iaitu memiliki pengetahuan teknikal, kemahiran dan insaniah.">
                                                                            <i class="fas fa-tools me-1"></i>TVET
                                                                        </span>
                                                                    <?php endif; ?>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                        
                                                        

                                                <!-- Executive Data Labels Section -->
                                                <div class="executive-data-section mb-4">
                                                    <div class="row g-3">
                                                        <!-- Kod Program -->
                                                        <div class="col-md-4">
                                                            <div class="executive-data-card">
                                                                <div class="executive-data-icon">
                                                                    <i class="fas fa-tag"></i>
                                                                </div>
                                                                <div class="executive-data-content">
                                                                    <span class="executive-data-label">KOD PROGRAM</span>
                                                                    <span class="executive-data-value"><?php echo e($PROGRAM->kod_Program); ?></span>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Tahun -->
                                                        <div class="col-md-4">
                                                            <div class="executive-data-card">
                                                                <div class="executive-data-icon">
                                                                    <i class="far fa-calendar-alt"></i>
                                                                </div>
                                                                <div class="executive-data-content">
                                                                    <span class="executive-data-label">TAHUN</span>
                                                                    <span class="executive-data-value"><?php echo e(session()->get('tahun_semasa')); ?></span>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Merit -->
                                                        <?php if(substr(Request::route('kodkatag'),0,1)!='G' && substr(Request::route('kodkatag'),0,1)!='E' && substr(Request::route('kodkatag'),0,1)!='F'): ?>
                                                        <div class="col-md-4">
                                                            <div class="executive-data-card">
                                                                <div class="executive-data-icon">
                                                                    <i class="fas fa-chart-line"></i>
                                                                </div>
                                                                <div class="executive-data-content">
                                                                    <span class="executive-data-label">PURATA MARKAH MERIT</span>
                                                                    <span class="executive-data-value">
                                                                        <?php $__currentLoopData = $MAKLUMAT_PENGAJIAN; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $maklumat_Pengajian): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <?php if($maklumat_Pengajian->kod_Program == $PROGRAM->kod_Program): ?>
                                                                                <?php if($maklumat_Pengajian->merit_Program==''): ?>
                                                                                    Tiada
                                                                                <?php else: ?>
                                                                                    <?php echo $maklumat_Pengajian->merit_Program; ?>%
                                                                                    <i class="fas fa-info-circle text-primary ms-1"
                                                                                       data-bs-toggle="tooltip"
                                                                                       data-bs-placement="top"
                                                                                       title="Tiada jaminan mendapat tawaran berdasarkan markah merit semata-mata."></i>
                                                                                <?php endif; ?>
                                                                            <?php endif; ?>
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <!-- Executive Action Buttons Section -->
                                                <div class="executive-action-buttons-container">
                                                    <div class="row g-2">
                                                        <!-- Syarat Button -->
                                                        <div class="col-6 col-lg-auto">
                                                            <?php if(session()->get('jenprog') == 'spm'): ?>
                                                                <a href="<?php echo e(url('ProgramPengajian/modalSyarat/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->kod_Program)); ?>"
                                                                   id="kelayakanMinimum_Modal"
                                                                   data-bs-toggle="modal"
                                                                   data-bs-target="#subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                   class="btn btn-outline-primary executive-action-btn"
                                                                   onclick="event.stopPropagation();">
                                                                    <i class="fas fa-book me-2"></i>SYARAT
                                                                </a>
                                                            <?php elseif(session()->get('jenprog') == 'stpm'): ?>
                                                                <?php if($PROGRAM->kategori_Pengajian=='G' || $PROGRAM->kategori_Pengajian=='E' || $PROGRAM->kategori_Pengajian=='F'): ?>
                                                                    <a href="<?php echo e(url('ProgramPengajian/modalSyaratdiploma/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->jensetaraf . '/' . $PROGRAM->kod_Program)); ?>"
                                                                       id="kelayakanMinimum_Modal"
                                                                       data-bs-toggle="modal"
                                                                       data-bs-target="#subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                       class="btn btn-outline-primary executive-action-btn"
                                                                       onclick="event.stopPropagation();">
                                                                        <i class="fas fa-book me-2"></i>SYARAT
                                                                    </a>
                                                                <?php else: ?>
                                                                    <a href="<?php echo e(url('ProgramPengajian/modalSyarat/' . $PROGRAM->kategori_Pengajian . '/' . $PROGRAM->kod_Program)); ?>"
                                                                       id="kelayakanMinimum_Modal"
                                                                       data-bs-toggle="modal"
                                                                       data-bs-target="#subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                       class="btn btn-outline-primary executive-action-btn"
                                                                       onclick="event.stopPropagation();">
                                                                        <i class="fas fa-book me-2"></i>SYARAT
                                                                    </a>
                                                                <?php endif; ?>
                                                            <?php endif; ?>
                                                        </div>

                                                        <!-- Yuran Button -->
                                                        <div class="col-6 col-lg-auto">
                                                            <a href="javascript:void(0)"
                                                               data-bs-toggle="modal"
                                                               data-bs-target="#yuran-pengajian"
                                                               class="btn btn-outline-success executive-action-btn"
                                                               onclick="event.stopPropagation();">
                                                                <i class="fas fa-dollar-sign me-2"></i>YURAN
                                                            </a>
                                                        </div>

                                                        <!-- Kampus Button -->
                                                        <div class="col-6 col-lg-auto">
                                                            <a href="javascript:void(0)"
                                                               data-bs-toggle="modal"
                                                               data-bs-target="#kampus__<?php echo e($PROGRAM->kod_Program); ?>"
                                                               class="btn btn-outline-info executive-action-btn"
                                                               onclick="event.stopPropagation();">
                                                                <i class="fas fa-map-marker-alt me-2"></i>KAMPUS
                                                            </a>
                                                        </div>

                                                        <!-- Kerjaya Button -->
                                                        <div class="col-6 col-lg-auto">
                                                            <a href="javascript:void(0)"
                                                               data-bs-toggle="modal"
                                                               data-bs-target="#laluan-kerjaya_<?php echo e($PROGRAM->kod_Program); ?>"
                                                               class="btn btn-outline-warning executive-action-btn"
                                                               onclick="event.stopPropagation();">
                                                                <i class="fas fa-briefcase me-2"></i>KERJAYA
                                                            </a>
                                                        </div>

                                                        <!-- Info Button -->
                                                        <div class="col-6 col-lg-auto">
                                                            <a href="javascript:void(0)"
                                                               data-bs-toggle="modal"
                                                               data-bs-target="#maklumatProgram__<?php echo e($PROGRAM->kod_Program); ?>"
                                                               class="btn btn-outline-secondary executive-action-btn"
                                                               onclick="event.stopPropagation();">
                                                                <i class="fas fa-info-circle me-2"></i>INFO
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                                       <?php if(substr(Request::route('kodkatag'),0,1)=='G'): ?>
                                                        <div class="form-row mt-n3">
                                                            <div class="form-group mr-1 mb-1">
                                                                <a
                                                                    href="javascript:void(0)" data-toggle="modal"
                                                                    data-target="#bidangNEC_<?php echo e($PROGRAM->kod_Program); ?>">
                                                                    <div class="col_badge btn btn-outline-primary">
                                                                        <i class="fas fa-bullseye" style="color:#000 !important;"></i> Bidang NEC
                                                                    </div>
                                                                </a>
                                                            </div>
                                                            <div class="form-group">

                                                            </div>
                                                        </div>
                                                        <?php endif; ?>








                                        <ul class="list-unstyled mb-0 flex-wrap d-none">
                                                           
                                                           
                                                            <?php if(session()->get('jenprog') == 'spm'): ?>
                                                              
                                                                <div class="modal fade kelayakanMinimum_Modal"
                                                                    id="subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                    tabindex="-1" role="dialog"
                                                                    aria-labelledby="syarat-program-title"
                                                                    aria-hidden="true">
                                                                    <div class="modal-dialog modal-dialog-centered modal-llgg"
                                                                        role="document">
                                                                        <div
                                                                            class="modal-content rounded shadow-lg border-0 overflow-hidden">
                                                                            <button type="button"
                                                                                class="close float-right mr-2"
                                                                                data-dismiss="modal" aria-label="Close"
                                                                                style="display:flex; justify-content:flex-end; width:100%; padding:0.9rem; margin-top: -0.8rem;">
                                                                                <span aria-hidden="true">&times;</span>
                                                                            </button>
                                                                            <div class="modal-body" style="height: 90vh; overflow-y: auto;">
                                                                                <div class="text-left">
                                                                                    <h4 class="text-center"><b>Syarat
                                                                                            Program</b>
                                                                                    </h4>
                                                                                    <div class="container mt-100 mt-60">
                                                                                        <div class="row">
                                                                                            <div class="col-12">
                                                                                                <ul class="nav nav-pills shadow flex-column flex-sm-row mb-0 p-1 bg-white rounded overflow-hidden"
                                                                                                    id="pills-tab"
                                                                                                    role="tablist">
                                                                                                    <!--Syarat Am Tab-->
                                                                                                    <li
                                                                                                        class="nav-item col-sm-6">
                                                                                                        <a class="nav-link py-2 active rounded"
                                                                                                            id="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            data-toggle="pill"
                                                                                                            href="#syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            role="tab"
                                                                                                            aria-controls="syarat-am"
                                                                                                            aria-selected="false">
                                                                                                            <div
                                                                                                                class="text-center">
                                                                                                                <h6
                                                                                                                    class="mb-0">
                                                                                                                    Syarat Am</h6>
                                                                                                            </div>
                                                                                                        </a>
                                                                                                    </li>

                                                                                                    <!--Syarat Khas Tab-->
                                                                                                    <li
                                                                                                        class="nav-item col-sm-6">
                                                                                                        <a class="nav-link py-2 rounded"
                                                                                                            id="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            data-toggle="pill"
                                                                                                            href="#syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                            role="tab"
                                                                                                            aria-controls="syarat-Khas"
                                                                                                            aria-selected="false">
                                                                                                            <div
                                                                                                                class="text-center">
                                                                                                                <h6
                                                                                                                    class="mb-0">
                                                                                                                    Syarat Khas
                                                                                                                </h6>
                                                                                                            </div>
                                                                                                        </a>
                                                                                                    </li>
                                                                                                </ul>

                                                                                                <div class="tab-content"
                                                                                                    id="pills-tabContent"
                                                                                                    style="padding-top: 2rem!important">
                                                                                                    <!--Paparan Syarat Am Tab-->
                                                                                                    <div class="card border-0 tab-pane fade show active"
                                                                                                        id="syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tabpanel"
                                                                                                        aria-labelledby="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>">

                                                                                                        <?php echo $__env->make('programPengajian.syarat_am_spm', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                        

                                                                                                    </div>

                                                                                                    <!--Paparan Syarat khas Tab-->
                                                                                                    <div class="card border-0 tab-pane fade"
                                                                                                        id="syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tabpanel"
                                                                                                        aria-labelledby="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>">
                                                                                                        <div class="text-muted contentLoad"
                                                                                                            style="font-weight: bold">
                                                                                                            <div
                                                                                                                align="center">
                                                                                                                <div
                                                                                                                    class="loader-spinner text-center">
                                                                                                                </div>
                                                                                                                <h4>Sila
                                                                                                                    tunggu
                                                                                                                    sebentar...
                                                                                                                </h4>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                    </p>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>



                                                            <?php elseif(session()->get('jenprog') == 'stpm'): ?>



                                                           

                                                            <div class="modal fade kelayakanMinimum_Modal"
                                                                id="subjek_<?php echo e($PROGRAM->kod_Program); ?>"
                                                                tabindex="-1" role="dialog"
                                                                aria-labelledby="syarat-program-title"
                                                                aria-hidden="true">
                                                                <div class="modal-dialog modal-dialog-centered modal-llgg"
                                                                    role="document" >
                                                                    <div
                                                                        class="modal-content rounded shadow-lg border-0 overflow-hidden">
                                                                        <button type="button"
                                                                            class="close float-right mr-2"
                                                                            data-dismiss="modal" aria-label="Close"
                                                                            style="display:flex; justify-content:flex-end; width:100%; padding:0.9rem; margin-top: -0.8rem;">
                                                                            <span aria-hidden="true">&times;</span>
                                                                        </button>
                                                                        <div class="modal-body" style="height: 90vh; overflow-y: auto;">
                                                                            <div class="text-left">
                                                                                <h4 class="text-center"><b>Syarat
                                                                                        Program</b>
                                                                                </h4>
                                                                                <div class="container mt-100 mt-60">
                                                                                    <div class="row">
                                                                                        <div class="col-12">
                                                                                            <ul class="nav nav-pills shadow flex-column flex-sm-row mb-0 p-1 bg-white rounded overflow-hidden"
                                                                                                id="pills-tab"
                                                                                                role="tablist">
                                                                                                <!--Syarat Am Tab-->
                                                                                                <li <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?> class="nav-item col-4" <?php else: ?> class="nav-item col-6" <?php endif; ?>>
                                                                                                    <a class="nav-link py-2 active rounded"
                                                                                                        id="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-am"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Am</h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>

                                                                                                <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?>
                                                                                                <li <?php echo e($PROGRAM->kod_Program == 'UM6143001' ? 'hidden' : null); ?>

                                                                                                    class="nav-item col-4">
                                                                                                    <a class="nav-link py-2 rounded"
                                                                                                        id="syarat-pendidikan-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-pendidikan-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-pendidikan"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Pendidikan</h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>
                                                                                                <?php endif; ?>


                                                                                                <!--Syarat Khas Tab-->
                                                                                                <li <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?> class="nav-item col-4" <?php else: ?> class="nav-item col-6" <?php endif; ?>>
                                                                                                    <a class="nav-link py-2 rounded"
                                                                                                        id="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        data-toggle="pill"
                                                                                                        href="#syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                        role="tab"
                                                                                                        aria-controls="syarat-Khas"
                                                                                                        aria-selected="false">
                                                                                                        <div
                                                                                                            class="text-center">
                                                                                                            <h6 class="mb-0">Syarat Khas
                                                                                                            </h6>
                                                                                                        </div>
                                                                                                    </a>
                                                                                                </li>
                                                                                            </ul>

                                                                                            <div class="tab-content"
                                                                                                id="pills-tabContent"
                                                                                                style="padding-top: 2rem!important">
                                                                                                <!--Paparan Syarat Am Tab-->
                                                                                                <div class="card border-0 tab-pane fade show active"
                                                                                                    id="syarat-am-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-am-tab-<?php echo e($PROGRAM->kod_Program); ?>">

                                                                                                    <?php if($PROGRAM->kategori_Pengajian=='G'): ?>
																										<?php echo $__env->make('programPengajian.syarat_am_diploma_g', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php elseif($PROGRAM->kategori_Pengajian=='E'): ?>
																										<?php echo $__env->make('programPengajian.syarat_am_diploma_e', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php elseif($PROGRAM->kategori_Pengajian=='F'): ?>
																										<?php echo $__env->make('programPengajian.syarat_am_diploma_f', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php else: ?>
																										<?php echo $__env->make('programPengajian.syarat_am_stpm', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                    <?php endif; ?>

                                                                                                </div>

                                                                                                <?php if(substr($PROGRAM->bidang_NEC, 0, 2) == '01'): ?>
                                                                                                <div class="card border-0 tab-pane fade"
                                                                                                    id="syarat-pendidikan-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-pendidikan-tab-<?php echo e($PROGRAM->kod_Program); ?>">

                                                                                                    <?php echo $__env->make('programPengajian.syarat_am_pendidikan', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                                                                </div>
                                                                                                <?php endif; ?>


                                                                                                <!--Paparan Syarat khas Tab-->
                                                                                                <div class="card border-0 tab-pane fade"
                                                                                                    id="syarat-Khas-<?php echo e($PROGRAM->kod_Program); ?>"
                                                                                                    role="tabpanel"
                                                                                                    aria-labelledby="syarat-Khas-tab-<?php echo e($PROGRAM->kod_Program); ?>">
                                                                                                    <div class="text-muted contentLoad"
                                                                                                        style="font-weight: bold">
                                                                                                        <div
                                                                                                            align="center">
                                                                                                            <div
                                                                                                                class="loader-spinner text-center">
                                                                                                            </div>
                                                                                                            <h4>Sila
                                                                                                                tunggu
                                                                                                                sebentar...
                                                                                                            </h4>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                                </p>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>





                                                                
                                                            <?php endif; ?>
                                                            

                                                            
                                                            


                                                        </ul>
                                                    </div>
                                                </div>

                                                <!--end col-->
                                            </div>
                                            <!--end row-->
                                        </div>
                                        <!--end blog post-->
                                    </div>
                                    <?php echo $__env->make('programPengajian.modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <?php echo $__env->make('pageLock.tiadaMaklumat', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            <?php endif; ?>

                            <!-- PAGINATION START -->
                            <div class="col-12 mt-4 pt-2">
                                <?php echo $SENARAI_PROGRAM->links('programPengajian.list-paginator'); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>
    <script src="<?php echo e(asset('/assets/js/range-slider.js')); ?>"></script>

    <!-- Modern Executive Card Styling -->
    <style>
        /* Executive Modern Card */
        .executive-modern-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 16px;
            border: 1px solid #e9ecef;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            overflow: hidden;
            position: relative;
        }

        .executive-modern-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #007bff, #28a745, #17a2b8, #ffc107, #6c757d);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .executive-modern-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            border-color: #dee2e6;
        }

        .executive-modern-card:hover::before {
            opacity: 1;
        }

        /* Executive Logo Container */
        .executive-logo-container {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .executive-modern-card:hover .executive-logo-container {
            transform: scale(1.05);
        }

        /* Executive Program Header */
        .executive-program-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: #2c3e50;
            line-height: 1.3;
            margin-bottom: 0.75rem;
            letter-spacing: -0.02em;
        }

        .executive-university-name {
            font-size: 0.95rem;
            color: #6c757d;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .executive-interview-badge {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            animation: pulse 2s infinite;
        }

        @keyframes  pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* Executive Badges */
        .executive-badges-container {
            margin-bottom: 1.5rem;
        }

        .executive-badge-feeder {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.4rem 1rem;
            border-radius: 25px;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
        }

        .executive-badge-stem {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.4rem 1rem;
            border-radius: 25px;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }

        .executive-badge-tvet {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.4rem 1rem;
            border-radius: 25px;
            box-shadow: 0 2px 8px rgba(243, 156, 18, 0.3);
        }

        .executive-badge-competitive {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.4rem 1rem;
            border-radius: 25px;
            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
        }

        .executive-badge-btech {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.4rem 1rem;
            border-radius: 25px;
            box-shadow: 0 2px 8px rgba(155, 89, 182, 0.3);
        }

        /* Executive Data Section */
        .executive-data-section {
            border-top: 1px solid #f1f3f4;
            padding-top: 1.5rem;
            margin-top: 1rem;
        }

        .executive-data-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 1rem;
            height: 100%;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .executive-data-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, #007bff, #28a745);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .executive-data-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: #dee2e6;
        }

        .executive-data-card:hover::before {
            opacity: 1;
        }

        .executive-data-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }

        .executive-data-icon i {
            color: #6c757d;
            font-size: 1.1rem;
            transition: color 0.3s ease;
        }

        .executive-data-card:hover .executive-data-icon {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }

        .executive-data-card:hover .executive-data-icon i {
            color: white;
        }

        .executive-data-content {
            flex: 1;
        }

        .executive-data-label {
            display: block;
            font-size: 0.7rem;
            font-weight: 700;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.25rem;
        }

        .executive-data-value {
            display: block;
            font-size: 1rem;
            font-weight: 700;
            color: #2c3e50;
            line-height: 1.2;
        }

        /* Executive Action Buttons */
        .executive-action-buttons-container {
            border-top: 1px solid #f1f3f4;
            padding-top: 1.5rem;
            margin-top: 1.5rem;
        }

        .executive-action-btn {
            font-weight: 600;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding: 0.75rem 1.25rem;
            border-radius: 25px;
            border-width: 2px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            min-height: 48px;
            position: relative;
            overflow: hidden;
        }

        .executive-action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .executive-action-btn:hover::before {
            left: 100%;
        }

        .executive-action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            text-decoration: none;
        }

        .btn-outline-primary.executive-action-btn:hover {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border-color: #007bff;
            color: white;
        }

        .btn-outline-success.executive-action-btn:hover {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
            border-color: #28a745;
            color: white;
        }

        .btn-outline-info.executive-action-btn:hover {
            background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
            border-color: #17a2b8;
            color: white;
        }

        .btn-outline-warning.executive-action-btn:hover {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            border-color: #ffc107;
            color: #212529;
        }

        .btn-outline-secondary.executive-action-btn:hover {
            background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
            border-color: #6c757d;
            color: white;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .executive-program-title {
                font-size: 1.2rem;
            }

            .executive-university-name {
                font-size: 0.9rem;
            }

            .executive-action-btn {
                font-size: 0.8rem;
                padding: 0.6rem 1rem;
                min-height: 44px;
            }

            .executive-data-card {
                padding: 0.75rem;
            }

            .executive-data-icon {
                width: 40px;
                height: 40px;
                margin-right: 0.75rem;
            }

            .executive-logo-container {
                width: 60px;
                height: 60px;
            }
        }

        @media (max-width: 576px) {
            .executive-action-btn {
                font-size: 0.75rem;
                padding: 0.5rem 0.75rem;
                min-height: 40px;
            }

            .executive-program-title {
                font-size: 1.1rem;
            }

            .executive-data-card {
                padding: 0.6rem;
            }

            .executive-data-icon {
                width: 36px;
                height: 36px;
                margin-right: 0.6rem;
            }

            .executive-logo-container {
                width: 50px;
                height: 50px;
            }
        }
    </style>

    <!-- Modern Executive JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Bootstrap 5 tooltips
            if (typeof bootstrap !== 'undefined') {
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl, {
                        animation: true,
                        delay: { show: 300, hide: 100 },
                        html: true
                    });
                });
            }

            // Card click functionality
            window.handleCardClick = function(event, programCode) {
                // Prevent card click when clicking on buttons
                if (event.target.closest('.executive-action-btn') ||
                    event.target.closest('[data-bs-toggle="modal"]')) {
                    return;
                }

                // Add click effect
                const card = event.currentTarget;
                card.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    card.style.transform = '';
                }, 150);

                // Open the modal programmatically
                const modal = document.querySelector('#maklumatProgram__' + programCode);
                if (modal && typeof bootstrap !== 'undefined') {
                    const bsModal = new bootstrap.Modal(modal);
                    bsModal.show();
                }
            };

            // Card hover effects
            window.handleCardHover = function(element, isEntering) {
                const logo = element.querySelector('.executive-logo-container');
                const badges = element.querySelectorAll('.badge');

                if (isEntering) {
                    // Add hover effects
                    if (logo) {
                        logo.style.transform = 'scale(1.05) rotate(2deg)';
                    }
                    badges.forEach(badge => {
                        badge.style.transform = 'scale(1.05)';
                    });
                } else {
                    // Remove hover effects
                    if (logo) {
                        logo.style.transform = '';
                    }
                    badges.forEach(badge => {
                        badge.style.transform = '';
                    });
                }
            };

            // Action button interactions
            const actionButtons = document.querySelectorAll('.executive-action-btn');
            actionButtons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.02)';
                });

                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });

                button.addEventListener('click', function(e) {
                    e.stopPropagation();

                    // Add click animation
                    this.style.transform = 'translateY(-1px) scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'translateY(-3px) scale(1.02)';
                    }, 100);
                });
            });

            // Data card hover effects
            const dataCards = document.querySelectorAll('.executive-data-card');
            dataCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    const icon = this.querySelector('.executive-data-icon');
                    if (icon) {
                        icon.style.transform = 'scale(1.1) rotate(5deg)';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    const icon = this.querySelector('.executive-data-icon');
                    if (icon) {
                        icon.style.transform = '';
                    }
                });
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\epanduan\resources\views/programPengajian/index.blade.php ENDPATH**/ ?>