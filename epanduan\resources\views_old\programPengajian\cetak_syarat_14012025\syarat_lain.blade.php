@if (!empty($syarat_umur[0]))
<li style="padding-left: .3em;">                           
    @foreach ($syarat_umur as $umur) 
        @if ($loop->first)
            @if ($umur->Operasi_Umur == '>')  Berumur <b>{{$umur->Umur1}} tahun @if ($umur->bulan1 == '0') @else {{$umur->bulan1}} bulan @endif keatas</b>.
            @elseif($umur->Operasi_Umur=='<')  Berumur <b>{{$umur->Umur1}} tahun @if ($umur->bulan1 == '0')  @else {{$umur->bulan1}} bulan @endif kebawah</b>.
            @elseif($umur->Operasi_Umur=='B')  Berumur <b>{{$umur->Umur1}} tahun @if ($umur->bulan1 != '0') {{$umur->bulan1}} bulan @endif hingga {{$umur->Umur2}} tahun @if ($umur->bulan2 != '0') {{$umur->bulan2}} bulan @endif </b>.
            @endif
        @endif
    @endforeach
</li>
@endif

@if (!empty($syarat_3M[0]))                       
@foreach ($syarat_3M as $status_3M) 
	@if ($loop->first)
		@if ($status_3M->syarat_3M == 'Y') 
			<li style="padding-left: .3em;">Menduduki SPM.</li>  
			<li style="padding-left: .3em;">Boleh Membaca, Menulis, Mengira.</li> 
		@endif
	@endif
@endforeach
@endif


@if (!empty($syarat_kahwin[0]))
<li style="padding-left: .3em;">                           
    @foreach ($syarat_kahwin as $status_kahwin) 
        @if ($loop->first)
            @if ($status_kahwin->Taraf_perkahwinan == 'B') Taraf Perkahwinan : <b>Bujang</b> @endif
        @endif
    @endforeach
</li>
@endif

@if (!empty($syarat_jantina[0]))
<li style="padding-left: .3em;">                           
    @foreach ($syarat_jantina as $status_jantina) 
        @if ($loop->first)
            @if ($status_jantina->Jantina == 'L')  Jantina : <b>Lelaki</b> @elseif($status_jantina->Jantina=='P') Jantina : <b>Perempuan</b> @endif
        @endif
    @endforeach
</li>
@endif

@foreach ($program as $key => $senarai_program)
    <div>
        @if ($senarai_program->TEMUDUGA == 'Y')
            <li style="padding-left: .3em;">
                <b>Lulus ujian dan / atau temu duga</b> yang ditetapkan.
            </li>
        @endif
    </div>

    <div>
        @if ($senarai_program->OKU == 'T')
            <li style="padding-left: .3em;">
                <b>TIDAK</b> mempunyai kurang upaya fizikal / anggota sehingga menyukarkan kerja-kerja amali.
            </li>
        @endif
    </div>

    <div>
        @if ($senarai_program->BUMI == 'Y')
            <li style="padding-left: .3em;">
                Berketurunan Melayu, Anak Negeri Sabah, Anak Negeri Sarawak dan Orang Asli sahaja.
            </li>
        @endif
    </div>
    {{-- @if ($senarai_program->REMARKS != '' || $senarai_program->REMARKS != null) 
<div class="font-weight-bold mt-3">Catatan :</div>                                                                                               
    {!! $senarai_program->REMARKS !!}                                                                                    
@endif --}}
@endforeach
