<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\SenaraiAgensiController;
use App\Http\Controllers\MaklumatAgensiController;
use App\Http\Controllers\SenaraiKategoriController;
use App\Http\Controllers\ProgramPengajianController;
use App\Http\Controllers\CarianKategoriProgramController;
use App\Http\Controllers\HalamanUtamaController;
use App\Http\Controllers\KalkulatorMeritController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
Route::get('/clear-all', function() {
    Artisan::call('route:clear');
    Artisan::call('view:clear');
    Artisan::call('cache:clear');
    Artisan::call('config:clear');

    return "All caches (route, view, cache, config) are cleared!";
});

Route::get('/optimize', function() {
    Artisan::call('optimize');
    return "optimize Successful";
});

use Illuminate\Support\Facades\Http;


Route::get('/check-mohe', function () {
    $response = Http::head('https://online.mohe.gov.my/epanduan/');
    dd($response->status());
});




Route::get('/', function () {
    return view('home');
});

Route::get('/', [HalamanUtamaController::class, 'index']);
Route::get('/senaraiAgensi', [SenaraiAgensiController::class, 'index']);
Route::get('/kategoriCalon', [SenaraiKategoriController::class, 'index']);
Route::get('/kategoriCalon/{jenprog}', [SenaraiKategoriController::class, 'index'])->name('kategoriCalon');

Route::get('/maklumatAgensi/{nama_ipta}', [MaklumatAgensiController::class, 'index']);
Route::get('/carianNamaProgram', [CarianKategoriProgramController::class, 'index']);
Route::post('/carianNamaProgram/carianProgramRawak', [CarianKategoriProgramController::class, 'carianProgramRawak']);

Route::get('/carianNamaProgram/{kodipta}/{kodprogram}/{katag}/{jenprog}', [CarianKategoriProgramController::class, 'paparProgramRawak']);
Route::get('/carianNamaProgram/{kodipta}/{kodprogram}/{jensetaraf}/{jenprog}', [CarianKategoriProgramController::class, 'paparEFGProgramRawak']);

Route::group(['prefix' => 'ProgramPengajian'], function () {
    Route::get('/kategoriCalon/{kodkatag}', [ProgramPengajianController::class, 'index']);
    Route::get('/modalSyarat/{kodkatag}/{kod}', [ProgramPengajianController::class, 'modal_syarat']);
    Route::get('/modalSyaratdiploma/{kodkatag}/{kodsetaraf}/{kod}', [ProgramPengajianController::class, 'modal_syarat_diploma']);
    Route::post('/kategoriCalon/{kod}', [ProgramPengajianController::class, 'carianProgram']);
    Route::get('/kategoriCalon/{kodkatag}/sort/{sort_order}', [ProgramPengajianController::class, 'index']);
});


Route::group(['prefix' => 'kalkulatorMerit'], function () {
    Route::get('/spm', [KalkulatorMeritController::class, 'calcSPM']);
    Route::get('/stpm', [KalkulatorMeritController::class, 'calcSTPM']);
    Route::get('/diploma', [KalkulatorMeritController::class, 'calcDIPLOMA']);
    Route::get('/stam', [KalkulatorMeritController::class, 'calcSTAM']);
});

/* --------------------------------------------------------------------------------------------------------------------------------------------------- */
// 12112024-1623 : HIJACK UNTUK PAPARAN SYARAT DALAM UPUSELECT [TQ AZUAN!]
Route::get('/ailis/{kodkatag}/{kod}', function (Request $request, $kodkatag, $kod) {
    session(['jenprog' => 'spm']);

    return app(ProgramPengajianController::class)->modal_syarat(request(), $kodkatag, $kod);
});
