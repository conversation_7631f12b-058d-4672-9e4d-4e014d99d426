@if ($paginator->hasPages())
    <ul class="pagination justify-content-center mb-0" style="margin-top: 2.5rem">
        <!-- Previous Page Link -->
        @if ($paginator->onFirstPage())
            <li class="page-item disabled">
                <a class="page-link" aria-label="Sebelum">
                    <i class="mdi mdi-arrow-left"></i>
                    Sebelum
                </a>
            </li>
        @else
            <li class="page-item">
                <a class="page-link" href="{{ $paginator->previousPageUrl() }}" aria-label="Sebelum">
                    <i class="mdi mdi-arrow-left"></i>
                    Sebelum
                </a>
            </li>
        @endif

        <!-- Pagination Elements Here -->
        @if ($paginator->lastPage() > 1)
            @if ($paginator->currentPage() > 3)
                <li class="page-item">
                    <a class="page-link" href="{{ $paginator->url(1) }}">1</a>
                </li>
            @endif
            @if ($paginator->currentPage() > 4)
                <li class="page-item">
                    <span class="page-link">...</span>
                </li>
            @endif
            @foreach(range(1, $paginator->lastPage()) as $i)
                @if($i >= $paginator->currentPage() - 2 && $i <= $paginator->currentPage() + 2)
                    @if ($i == $paginator->currentPage())
                        <li class="page-item active">
                            <a class="page-link">{{ $i }}</a>
                        </li>
                    @else
                        <li class="page-item">
                            <a class="page-link" href="{{ $paginator->url($i) }}">{{ $i }}</a>
                        </li>
                    @endif
                @endif
            @endforeach
            @if ($paginator->currentPage() < $paginator->lastPage() - 3)
                <li class="page-item">
                    <span class="page-link">...</span>
                </li>
            @endif
            @if ($paginator->currentPage() < $paginator->lastPage() - 2)
                <li class="page-item">
                    <a class="page-link" href="{{ $paginator->url($paginator->lastPage()) }}">{{ $paginator->lastPage() }}</a>
                </li>
            @endif
        @endif

        <!-- Next Page Link -->
        @if ($paginator->hasMorePages())
            <li class="page-item">
                <a class="page-link" href="{{ $paginator->nextPageUrl() }}" aria-label="Seterusnya">
                    Seterusnya
                    <i class="mdi mdi-arrow-right"></i>
                </a>
            </li>
        @else
            <li class="page-item disabled">
                <a class="page-link" aria-label="Seterusnya">
                    Seterusnya
                    <i class="mdi mdi-arrow-right"></i>
                </a>
            </li>
        @endif
    </ul>
@endif
