// merit-threshold.js
document.addEventListener("DOMContentLoaded", function() {
    // Minimum Threshold
    document.getElementById("minMerit").addEventListener("input", function() {
      document.getElementById("minDisplay").textContent = this.value;
      const maxValue = parseFloat(document.getElementById("maxMerit").value);
      if (parseFloat(this.value) > maxValue) {
        this.value = maxValue;
        document.getElementById("minDisplay").textContent = maxValue;
      }
    });

    // Maximum Threshold
    document.getElementById("maxMerit").addEventListener("input", function() {
      document.getElementById("maxDisplay").textContent = this.value;
      const minValue = parseFloat(document.getElementById("minMerit").value);
      if (parseFloat(this.value) < minValue) {
        this.value = minValue;
        document.getElementById("maxDisplay").textContent = minValue;
      }
    });
  });
