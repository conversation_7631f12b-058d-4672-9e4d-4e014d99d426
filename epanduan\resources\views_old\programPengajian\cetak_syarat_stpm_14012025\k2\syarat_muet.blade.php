
@if($PROGRAM->kate<PERSON><PERSON>_Pengajian!='G' && $PROGRAM->kate<PERSON><PERSON>_Pengajian!='E' && $PROGRAM->kategor<PERSON>_Pengajian!='F')
    @foreach ($syarat_muet_2 as $syaratmuet_2)
        <div style="padding-left: .3em; margin-bottom:8px;">                           
            Mendapat sekurang-kurangnya 
            <b> 
                @foreach ($codeset_muet2 as $muet2) 
                    @if($syaratmuet_2->MUET1_BAND==$muet2->kodthpmuet)
                    {{$muet2->ketthpmuet}}
                    @endif
                @endforeach
            </b>
            untuk keputusan yang diperolehi mulai sesi 1 Tahun {{$syaratmuet_2->MUET1_Tahun}}  
            <b> 
                atau
                @foreach ($codeset_muet as $muet) 
                    @if($syaratmuet_2->MUET2_Band==$muet->kodthpmuet)
                    {{$muet->ketthpmuet}}
                    @endif
                @endforeach
            </b>
            untuk keputusan Tahun {{$syaratmuet_2->MUET2_Tahun}} dan sebelum dalam <b>Malaysian University Engdivsh Test (MUET) mengikut tempoh sah laku pada tarikh permohonan</b>. 
        </div>
    @endforeach

	{!! $operator402 !!}

    @foreach ($syarat_1119_2 as $syarat1119_2)
        <div style="padding-left: .3em; margin-bottom:8px;">                           
            Mendapat sekurang-kurangnya Gred <b>{{$syarat1119_2->gred_1119}}</b> dalam mata pelajaran Bahasa Inggeris pada peringkat <b>SPM</b>.
        </div>
    @endforeach
@endif
