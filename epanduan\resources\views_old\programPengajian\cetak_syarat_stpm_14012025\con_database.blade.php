@php
   
// TIADA KUMPULAN
$syaratkhas_nn_stpm_0 = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_nn_stpm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID','ASC')->get();
$syaratkhas_nn_spm_0 = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_nn_spm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID','ASC')->get();
$syaratkhas_k0_g1 = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_g1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k0_ga = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_ga')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k0_g2 = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_g2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k0_g3 = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_g3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_sk0 = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_sk1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
$syaratkhas_k0_f1 = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k0_f2 = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k0_f3 = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 									
$syaratkhas_k0_f4 = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf4')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k0_f5 = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf5')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 								
$syarat_muet_k0 = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_rmuet')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('Syarat_MUET','1')->where('sesi',session()->get('sesi_semasa'))->get();									
$syarat_1119_k0 = DB::connection('kpt')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('Syarat_1119','1')->where('sesi',session()->get('sesi_semasa'))->get();
$syarat_umur_k0 = DB::connection('kpt')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('Syarat_Umur','1')->where('sesi',session()->get('sesi_semasa'))->get();									
$syarat_kahwin_k0 = DB::connection('kpt')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->whereNotIn(DB::raw('substr(PROGRAMKOD, -3, 1)'), ['1','2','3','4'])->where('Syarat_taraf_perkahwinan','1')->where('sesi',session()->get('sesi_semasa'))->get();													

// JIKA KUMPULAN 1
$syaratkhas_nn_stpm_1  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_nn_stpm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID','ASC')->get();
$syaratkhas_nn_spm_1 = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_nn_spm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID','ASC')->get();
$syaratkhas_k1_g1  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_g1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k1_ga  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_ga')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k1_g2  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_g2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k1_g3  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_g3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();  
$syaratkhas_sk1  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_sk1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k1_f1  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k1_f2  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k1_f3  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();								
$syaratkhas_k1_f4  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf4')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k1_f5  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf5')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '1')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
$syarat_muet_k1  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_rmuet')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('Syarat_MUET','1')->where(DB::raw('substr(Programkod, -3, 1)'), '1')->where('sesi',session()->get('sesi_semasa'))->get();
$syarat_1119_k1  = DB::connection('kpt')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('Syarat_1119','1')->where(DB::raw('substr(Programkod, -3, 1)'), '1')->where('sesi',session()->get('sesi_semasa'))->get();
$syarat_umur_k1  = DB::connection('kpt')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('Syarat_Umur','1')->where(DB::raw('substr(Programkod, -3, 1)'), '1')->where('sesi',session()->get('sesi_semasa'))->get();
$syarat_kahwin_k1  = DB::connection('kpt')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('Syarat_taraf_perkahwinan','1')->where(DB::raw('substr(Programkod, -3, 1)'), '1')->where('sesi',session()->get('sesi_semasa'))->get();																											

// JIKA KUMPULAN 2
$syaratkhas_nn_stpm_2  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_nn_stpm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID','ASC')->get();
$syaratkhas_nn_spm_2 = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_nn_spm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID','ASC')->get();
$syaratkhas_k2_g1  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_g1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k2_ga  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_ga')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k2_g2  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_g2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k2_g3  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_g3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_sk2  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_sk1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k2_f1  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k2_f2  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k2_f3  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k2_f4  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf4')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k2_f5  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf5')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '2')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syarat_muet_k2  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_rmuet')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('Syarat_MUET','1')->where(DB::raw('substr(Programkod, -3, 1)'), '2')->where('sesi',session()->get('sesi_semasa'))->get();
$syarat_1119_k2  = DB::connection('kpt')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('Syarat_1119','1')->where(DB::raw('substr(Programkod, -3, 1)'), '2')->where('sesi',session()->get('sesi_semasa'))->get();
$syarat_umur_k2  = DB::connection('kpt')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('Syarat_Umur','1')->where(DB::raw('substr(Programkod, -3, 1)'), '2')->where('sesi',session()->get('sesi_semasa'))->get();	
$syarat_kahwin_k2  = DB::connection('kpt')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('Syarat_taraf_perkahwinan','1')->where(DB::raw('substr(Programkod, -3, 1)'), '2')->where('sesi',session()->get('sesi_semasa'))->get();

// JIKA KUMPULAN 3
$syaratkhas_nn_stpm_3  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_nn_stpm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID','ASC')->get();
$syaratkhas_nn_spm_3 = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_nn_spm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID','ASC')->get();
$syaratkhas_k3_g1  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_g1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k3_ga  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_ga')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k3_g2  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_g2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k3_g3  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_g3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_sk3  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_sk1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
$syaratkhas_k3_f1  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k3_f2  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k3_f3  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k3_f4  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf4')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k3_f5  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf5')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '3')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syarat_muet_k3  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_rmuet')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('Syarat_MUET','1')->where(DB::raw('substr(Programkod, -3, 1)'), '3')->where('sesi',session()->get('sesi_semasa'))->get();								
$syarat_1119_k3  = DB::connection('kpt')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('Syarat_1119','1')->where(DB::raw('substr(Programkod, -3, 1)'), '3')->where('sesi',session()->get('sesi_semasa'))->get();
$syarat_umur_k3  = DB::connection('kpt')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('Syarat_Umur','1')->where(DB::raw('substr(Programkod, -3, 1)'), '3')->where('sesi',session()->get('sesi_semasa'))->get();								
$syarat_kahwin_k3  = DB::connection('kpt')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('Syarat_taraf_perkahwinan','1')->where(DB::raw('substr(Programkod, -3, 1)'), '3')->where('sesi',session()->get('sesi_semasa'))->get();

// JIKA KUMPULAN 4
$syaratkhas_nn_stpm_4  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_nn_stpm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID','ASC')->get();
$syaratkhas_nn_spm_4 = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_nn_spm')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID','ASC')->get();
$syaratkhas_k4_g1  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_g1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k4_ga  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_ga')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k4_g2  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_g2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k4_g3  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_g3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_sk4  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_sk1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get();
$syaratkhas_k4_f1  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf1')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k4_f2  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf2')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k4_f3  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf3')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k4_f4  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf4')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syaratkhas_k4_f5  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tf5')->where(DB::raw('substr(PROGRAMKOD, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(PROGRAMKOD, -2, 1)'), $print->kategori)->where(DB::raw('substr(PROGRAMKOD, -3, 1)'), '4')->where('SESI', session()->get('sesi_semasa'))->orderby('ORDERID2','ASC')->get(); 
$syarat_muet_k4  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_rmuet')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('Syarat_MUET','1')->where(DB::raw('substr(Programkod, -3, 1)'), '4')->where('sesi',session()->get('sesi_semasa'))->get();								
$syarat_1119_k4  = DB::connection('kpt')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('Syarat_1119','1')->where(DB::raw('substr(Programkod, -3, 1)'), '4')->where('sesi',session()->get('sesi_semasa'))->get();
$syarat_umur_k4  = DB::connection('kpt')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('Syarat_Umur','1')->where(DB::raw('substr(Programkod, -3, 1)'), '4')->where('sesi',session()->get('sesi_semasa'))->get();								
$syarat_kahwin_k4  = DB::connection('kpt')->table('syarat_lain')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('Syarat_taraf_perkahwinan','1')->where(DB::raw('substr(Programkod, -3, 1)'), '4')->where('sesi',session()->get('sesi_semasa'))->get(); 

$program  = DB::connection('kpt')->table('program_stpm')->where(DB::raw('substr(KODPROGRAM, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(KODPROGRAM, -2, 1)'), $print->kategori)->where('STATUS_TAWAR','Y')->where('sesi',session()->get('sesi_semasa'))->groupby('KODPROGRAM_PAPAR')->get();

$program_1  = DB::connection('kpt')->table('program_stpm')->where(DB::raw('substr(KODPROGRAM, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(KODPROGRAM, -2, 1)'), $print->kategori)->whereNotIn(DB::raw('substr(KODPROGRAM, -3, 1)'), ['1','2','3','4'])->where('STATUS_TAWAR','Y')->where('sesi',session()->get('sesi_semasa'))->groupby('KODPROGRAM_PAPAR')->get();
$program_2  = DB::connection('kpt')->table('program_stpm')->where(DB::raw('substr(KODPROGRAM, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(KODPROGRAM, -2, 1)'), $print->kategori)->where(DB::raw('substr(KODPROGRAM, -3, 1)'), '1')->where('STATUS_TAWAR','Y')->where('sesi',session()->get('sesi_semasa'))->groupby('KODPROGRAM_PAPAR')->get();

$syarat_diploma  = DB::connection('kpt')->table('syarat_khas_diploma')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where('KATEGORI',$print->kategori)->where('JENSETARAF',$print->jensetaraf)->where('sesi',session()->get('sesi_semasa'))->get(); 
$syarat_stpm_matrik  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_pngk')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('Syarat_PNGK_STPM','1')->where('sesi',session()->get('sesi_semasa'))->get();		
$syarat_stam  = DB::connection('kpt')->table('upuplus_papar_syarat_stpm_tstam')->where(DB::raw('substr(Programkod, 1, 9)'), $print->kodprogram)->where(DB::raw('substr(Programkod, -2, 1)'), $print->kategori)->where('syarat_tahap_STAM','1')->where('sesi',session()->get('sesi_semasa'))->get();
@endphp