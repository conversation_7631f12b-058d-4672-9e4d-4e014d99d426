@foreach ($SENARAI_PROGRAM as $PROGRAM)
    @php

    $sessionSesi = session()->get('sesi_semasa');

    include(app_path() . '/Http/Controllers/include_papar_spm/syaratkhas_nn.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/syaratkhas_valid_g1.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/syaratkhas_valid_ga.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/syaratkhas_valid_g2.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/syaratkhas_valid_gb.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/syaratkhas_valid_g3.php');


    // JIKA TIADA KUMPULAN
    include(app_path() . '/Http/Controllers/include_papar_spm/k0/syaratkhas_g1.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/k0/syaratkhas_ga.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/k0/syaratkhas_g2.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/k0/syaratkhas_gb.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/k0/syaratkhas_g3.php');

    // JIKA KUMPULAN 1
    include(app_path() . '/Http/Controllers/include_papar_spm/k1/syaratkhas_k1_g1.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/k1/syaratkhas_k1_ga.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/k1/syaratkhas_k1_g2.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/k1/syaratkhas_k1_gb.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/k1/syaratkhas_k1_g3.php');

    // JIKA KUMPULAN 2
    include(app_path() . '/Http/Controllers/include_papar_spm/k2/syaratkhas_k2_g1.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/k2/syaratkhas_k2_ga.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/k2/syaratkhas_k2_g2.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/k2/syaratkhas_k2_gb.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/k2/syaratkhas_k2_g3.php');

    // JIKA KUMPULAN 3
    include(app_path() . '/Http/Controllers/include_papar_spm/k3/syaratkhas_k3_g1.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/k3/syaratkhas_k3_ga.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/k3/syaratkhas_k3_g2.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/k3/syaratkhas_k3_gb.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/k3/syaratkhas_k3_g3.php');

    include(app_path() . '/Http/Controllers/include_papar_spm/syaratkhas_sk1.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/syaratkhas_sk2.php');


    include(app_path() . '/Http/Controllers/include_papar_spm/syaratkhas_f1.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/syaratkhas_f2.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/syaratkhas_f3.php');

    include(app_path() . '/Http/Controllers/include_papar_spm/program.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/syarat_umur.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/syarat_kahwin.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/syarat_jantina.php');
    include(app_path() . '/Http/Controllers/include_papar_spm/syarat_3M.php');
    @endphp
@endforeach

<ol style="padding-left: 2em;" style="list-style-type:decimal;">

    @if(count($syaratkhas_nn) > 0)
        @include('programPengajian.cetak_syarat.syarat_khas_nn')
    @endif

    @if(count($syaratkhas_valid_g1) > 0 && count($syaratkhas_valid_ga) == 0)
        @include('programPengajian.cetak_syarat.syarat_khas_g1')
    @endif

    @if(count($syaratkhas_valid_g1) == 0 && count($syaratkhas_valid_ga) > 0)
        @include('programPengajian.cetak_syarat.syarat_khas_ga')
    @endif

    @if(count($syaratkhas_valid_g1) > 0 && count($syaratkhas_valid_ga) > 0 )
        @if(count($syaratkhas_sk1) > 0)
            @include('programPengajian.cetak_syarat.syarat_khas_sk1')
        @endif
    @endif


    @if(count($syaratkhas_valid_g2) > 0 && count($syaratkhas_valid_gb) == 0)
        @include('programPengajian.cetak_syarat.syarat_khas_g2')
    @endif

    @if(count($syaratkhas_valid_g2) == 0 && count($syaratkhas_valid_gb) > 0)
        @include('programPengajian.cetak_syarat.syarat_khas_gb')
    @endif

    @if(count($syaratkhas_valid_g2) > 0 && count($syaratkhas_valid_gb) > 0 )
        @if(count($syaratkhas_sk2) > 0)
            @include('programPengajian.cetak_syarat.syarat_khas_sk2')
        @endif
    @endif

    @if(count($syaratkhas_valid_g3) > 0)
        @include('programPengajian.cetak_syarat.syarat_khas_g3')
    @endif

    @if(count($syaratkhas_f3) > 0)
        @include('programPengajian.cetak_syarat.syarat_khas_f3')
    @endif

    @if(count($syaratkhas_f1) > 0)
        @include('programPengajian.cetak_syarat.syarat_khas_f1')
    @endif

    @if(count($syaratkhas_f2) > 0)
        @include('programPengajian.cetak_syarat.syarat_khas_f2')
    @endif

    @include('programPengajian.cetak_syarat.syarat_lain')
</ol>
