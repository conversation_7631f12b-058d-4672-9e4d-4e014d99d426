
@foreach ($syaratkhas_nn_spm_2 as $syarat_khas_nn_spm_2)
	@if(substr($syarat_khas_nn_spm_2->KODSUBJEK_1,0,1)!='K')
	<div style="padding-left: .3em; margin-bottom:8px;"> 
			Mendapat sekurang-kurangnya Gred <b>{{$syarat_khas_nn_spm_2->MINGRED}}</b> dalam mata pelajaran
			<b>
				{{ucwords(strtolower($syarat_khas_nn_spm_2->KODSUBJEK_2))}}
			</b>
			di peringkat <b>SPM</b>.
		</div>
	@endif

	@if(substr($syarat_khas_nn_spm_2->KODSUBJEK_1,0,1)=='K')
		<div style="padding-left: .3em; margin-bottom:8px;"> 
			Mendapat sekurang-kurangnya Gred <b>{{$syarat_khas_nn_spm_2->MINGRED}}</b> dalam <b>{{$syarat_khas_nn_spm_2->KET_JUMLAH_MIN_SUBJEK}} ({{$syarat_khas_nn_spm_2->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran di peringkat <b>SPM</b> :
			<div style="border:1px solid rgba(0, 0, 0, 0.125); border-radius:0.25rem; padding:0.25rem; background-color: #e9ecef52 !important; margin-top:5px;">
				<table cellpadding="2" width="100%">
					<tr>
						<td style="vertical-align:top;">&#8226;</td>
						<td style="vertical-align:top; width:95%">{{ ucwords(strtolower($syarat_khas_nn_spm_2->KODSUBJEK_2)) }}</td>
					</tr>
				</table>
			</div>		
		</div>
	@endif

	@if ($loop->first)
	{{-- {!! $operator203 !!} --}}
	@endif
@endforeach

