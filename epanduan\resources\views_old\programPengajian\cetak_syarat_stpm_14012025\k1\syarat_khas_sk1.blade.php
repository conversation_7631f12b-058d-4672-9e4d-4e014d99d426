
@foreach ($syaratkhas_sk_1 as $syarat_khas_sk_1)
    @if ($loop->first)
        <li style="padding-left: .3em; margin-bottom:8px;"> 
            Mendapat sekurang-kurangnya Gred <b>{{$syarat_khas_sk_1->MINGRED_1}}</b> dalam <b>{{$syarat_khas_sk_1->KET_JUMLAH_MIN_SUBJEK_1}} ({{$syarat_khas_sk_1->JUMLAH_MIN_SUBJEK_1}})</b> mata pelajaran <b>DAN</b> Gred <b>{{$syarat_khas_sk_1->MINGRED_2}}</b> dalam <b>{{$syarat_khas_sk_1->KET_JUMLAH_MIN_SUBJEK_2}} ({{$syarat_khas_sk_1->JUMLAH_MIN_SUBJEK_2}})</b> mata pelajaran di peringkat 
            
            <b>
                @if($PROGRAM->kate<PERSON>i_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S') STPM
                @elseif($PROGRAM->kategori_Pengajian=='T') STAM
                @elseif($PROGRAM->kategori_Pengajian=='N') Matrikulasi / Asasi
                @elseif($PROGRAM->kategori_Pengajian=='P' || $PROGRAM->kategori_Pengajian=='J') Matrikulasi
                @else Asasi
                @endif
            </b> :
            
            
            <div class="card bg-light text-dark">
                <div class="card-body p-2">
                    @foreach ($syaratkhas_sk_1 as $syaratkhas_sk1)
                        @if(substr($syaratkhas_sk1->PROGRAMKOD,-3,1)=='1')
                            <div class="col-md-12">
                                <span  style="display:table-cell;">&#9679;</span>
                                <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syaratkhas_sk1->KODSUBJEK_2)) }}</span>
                            </div>
                        @endif
                    @endforeach
                </div>
            </div>
        </li>
    @endif
@endforeach




