
@foreach ($syaratkhas_g1_2 as $syarat_khas_g1_2)
@if ($loop->first)
    <div style="padding-left: .3em; margin-bottom:8px;"> 
        Mendapat sekurang-kurangnya Gred <b>{{$syarat_khas_g1_2->MINGRED}}</b> dalam <b>{{$syarat_khas_g1_2->KET_JUMLAH_MIN_SUBJEK}} ({{$syarat_khas_g1_2->JUMLAH_MIN_SUBJEK}})</b> mata pelajaran di peringkat 
        <b>
            @if($PROGRAM->kategori_Pengajian=='A' || $PROGRAM->kategori_Pengajian=='S') STPM
            @elseif($PROGRAM->kategori_Pengajian=='T') STAM
            @elseif($PROGRAM->kategori_Pengajian=='N') <PERSON><PERSON><PERSON><PERSON> / Asasi
            @elseif($PROGRAM->kate<PERSON><PERSON>_Pengajian=='P' || $PROGRAM->kate<PERSON><PERSON>_Pengajian=='J') <PERSON><PERSON><PERSON><PERSON>
            @else Asasi
            @endif
        </b> :
        <div class="card bg-light text-dark">
            <div class="card-body p-2">
                @foreach ($syaratkhas_g1_2 as $syarat_khas_subjek)
                  
                        <div class="col-md-12">
                            <span  style="display:table-cell;">&#9679;</span>
                            <span style="padding-left:5px; display:table-cell;">{{ ucwords(strtolower($syarat_khas_subjek->KODSUBJEK_2)) }}</span>
                        </div>
                   
                @endforeach
            </div>
        </div>
    </div>
@endif
@endforeach

