@extends('layout.layout')
@section('content')

<style>
   /* @media (min-width: 992px) 
   {
        .container, .container-lg, .container-md, .container-sm {
           
        }
   } */


    .select2-container .select2-selection--single { border: none; }
    .select2 { width: 100% !important; }
    .select2-container--default .select2-results>.select2-results__options{ max-height: 400px; font-size: .875rem; } 
    .select2-container--default .select2-selection--single .select2-selection__rendered  { color: black; font-weight: normal; }
    .select2-container--default .select2-selection--single .select2-selection__arrow { height: 30px; }
    .select2-container .select2-selection--single .select2-selection__rendered {white-space: break-spaces;}
    .select2-container .select2-selection--single { height: 100%; }
    .select2-container--bootstrap .select2-selection{ border-radius: 0px; }
    
    .select2-container--default .select2-results__option[aria-disabled=true] { display: none;}
</style>

    <section class="bg-half bg-light d-table w-100">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-12 text-center">
                    <div class="page-next-level">
                        <h3 class="title"> KALKULATOR MERIT</h3>
                        <div class="page-next">
                            <nav aria-label="breadcrumb" class="d-inline-block">
                                <ul class="breadcrumb bg-white rounded shadow mb-0">
                                    <li class="breadcrumb-item"><a href="{{ url('/') }}">Laman Utama</a></li>
                                    <li class="breadcrumb-item">Kalkulator Merit</li>
                                    <li class="breadcrumb-item">
                                        @if(Request::path()=='kalkulatorMerit/spm') Lepasan SPM @endif
                                        @if(Request::path()=='kalkulatorMerit/stpm') Lepasan STPM / Setaraf @endif
                                        {{-- @if(Request::path()=='kalkulatorMerit/stam') STAM @endif --}}
                                        {{-- @if(Request::path()=='kalkulatorMerit/diploma') DIPLOMA @endif --}}
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="position-relative">
        <div class="shape overflow-hidden text-white">
            <svg viewBox="0 0 2880 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 48H1437.5H2880V0H2160C1442.5 52 720 0 720 0H0V48Z" fill="currentColor"></path>
            </svg>
        </div>
    </div>

    <section class="section">
        <div class="container" style="max-width: 600px;"> 

            @if(Request::path()=='kalkulatorMerit/stpm') 
                <div class="row form-group{{ $errors->has('KATEGORI') ? ' has-error' : '' }} mt-3">                    
                    <div class="col-xl-5">
                        {!! Html::decode(Form::label('KATEGORI', 'SILA PILIH JENIS KATEGORI : ',['class'=>'form-label form label-sm', 'style'=>'white-space: nowrap;'])) !!}
                    </div>                     
                    <div class="col-xl-7">                         
                        <div class="input-group input-group-sm">
                            <select name="KATEGORI" id="KATEGORI" class="form-control form-control-sm mt-n1" onchange="kategori(this);">
                                <option value="stpm" selected="selected">STPM / Matrikulasi / Asasi</option>
                                <option value="stam">STAM</option>
                                <option value="diploma">Diploma</option>
                            </select>                         
                        </div>                       
                    </div>                 
                </div>
            @endif

            @if(Request::path()=='kalkulatorMerit/spm')
                @include('kalkulatorMerit.merit_stem')
                <div class="modal-footer" style="display:block; text-align: center;">
                    <button type="reset" class="btn btn-danger" name="btnRESETSPM" id="btnRESETSPM" value="Reset" onclick="resetSPM()" style="font-size: 14px;"><i class="fa fa-fw fa-eraser"></i> Semula</button>
                    <button type="button" class="btn btn-success" name="btnKIRAMERITSPM" id="btnKIRAMERITSPM" value="Kira" onclick="kiraSPM()" style="font-size: 14px;">&nbsp;&nbsp;<i class="fa fa-fw fa-calculator"></i> Kira &nbsp;&nbsp; </button>
                </div>
                
            @endif

            @if(Request::path()=='kalkulatorMerit/stpm') 

                <span id="stpm_page">
                    @include('kalkulatorMerit.merit_stpm')
                    <div class="modal-footer" style="display:block; text-align: center;">
                        <button type="reset" class="btn  btn-danger" name="btnRESETSTPM" id="btnRESETSTPM" value="Reset" onclick="btnRESETSTPM()" style="font-size: 14px;"><i class="fa fa-fw fa-eraser"></i> Semula</button>
                        <button type="button" class="btn btn-success" name="btnKIRAMERITSTPM" id="btnKIRAMERITSTPM" value="Kira" onclick="kiraSTPM()" style="font-size: 14px;">&nbsp;&nbsp;<i class="fa fa-fw fa-calculator"></i> Kira &nbsp;&nbsp; </button>
                    </div>
                </span>
  
                <span id="diploma_page">
                    @include('kalkulatorMerit.merit_diploma')
                    <div class="modal-footer" style="display:block; text-align: center;">
                        <button type="reset" class="btn  btn-danger" name="btnRESETDIPLOMA" id="btnRESETDIPLOMA" value="Reset" onclick="btnRESETDIPLOMA()" style="font-size: 14px;"><i class="fa fa-fw fa-eraser"></i> Semula</button>
                        <button type="button" class="btn btn-success" name="btnKIRAMERITDIPLOMA" id="btnKIRAMERITDIPLOMA" value="Kira" onclick="kiraDIPLOMA()" style="font-size: 14px;">&nbsp;&nbsp;<i class="fa fa-fw fa-calculator"></i> Kira &nbsp;&nbsp; </button>
                    </div>
                </span>
   
                <span id="stam_page">
                    @include('kalkulatorMerit.merit_stam')
                    <div class="modal-footer" style="display:block; text-align: center;">
                        <button type="reset" class="btn  btn-danger" name="btnRESETSTAM" id="btnRESETSTAM" value="Reset" onclick="btnRESETSTAM()" style="font-size: 14px;"><i class="fa fa-fw fa-eraser"></i> Semula</button>
                        <button type="button" class="btn btn-success" name="btnKIRAMERITSTAM" id="btnKIRAMERITSTAM" value="Kira" onclick="kiraSTAM()" style="font-size: 14px;">&nbsp;&nbsp;<i class="fa fa-fw fa-calculator"></i> Kira &nbsp;&nbsp; </button>
                    </div>
                </span>
            @endif

        </div>
    </section>


<script>

    
   function isNumber(evt) 
   {
      evt = (evt) ? evt : window.event;
      var charCode = (evt.which) ? evt.which : evt.keyCode;
      if (charCode != 46 && charCode > 31 && (charCode < 48 || charCode > 57)) 
      {
         return false;
      }
         return true;         
   }  
</script>

@endsection
