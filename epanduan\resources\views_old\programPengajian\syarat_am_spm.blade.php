

@foreach ($j<PERSON><PERSON><PERSON><PERSON><PERSON> as $key => $jpengajian)

    @php
        $syaratam_am  = DB::connection('emas')->table('syarat_am_pengajian')->where('KOD_PENGAJIAN',$jpengajian->KOD_PENGAJIAN)->orderby('KOD_PENGAJIAN','ASC')->get();
    @endphp


    <div class="pl-2 pb-3" style="line-height:1.8rem; float:left; width: 90%; margin-top:-1px; margin-bottom: -35px;">

        <ol style="padding-left: 1.5em;">

            @if($jpengajian->KOD_PENGAJIAN!='S' && $PROGRAM->jpengajian==$jpengajian->KOD_PENGAJIAN)

                @foreach ($syaratam_am as $syaratam)
                    @if($syaratam->KODSUBJEK=='W')
                    <li>Warganegara Malaysia yang mempunyai <b>No. Kad <PERSON> / My Kad</b>.</li>
                    @endif
                @endforeach

				@if((substr($PROGRAM->kod_Program,0,2) =='FB' || substr($PROGRAM->kod_Program,0,2) =='FC') && $jpengajian->KOD_PENGAJIAN =='A')
					<li>Memiliki <b>Sijil Pelajaran Malaysia (SPM)</b> dengan mendapat sekurang-kurangnya :-</li>

				@elseif((substr($PROGRAM->kod_Program,0,2) =='FB' || substr($PROGRAM->kod_Program,0,2) =='FC') && $jpengajian->KOD_PENGAJIAN =='D2')
					<li>Memiliki <b>Sijil Pelajaran Malaysia (SPM)</b> dengan mendapat sekurang-kurangnya :-</li>


				@elseif((substr($PROGRAM->kod_Program,0,2) != 'FB' && substr($PROGRAM->kod_Program,0,2) != 'FC' && substr($PROGRAM->kod_Program,0,1) != 'U') && ($jpengajian->KOD_PENGAJIAN =='A'))
					<li>Memiliki <b>Sijil Pelajaran Malaysia (SPM)</b> dengan mendapat sekurang-kurangnya :-</li>

				@elseif((substr($PROGRAM->kod_Program,0,2) != 'FB' && substr($PROGRAM->kod_Program,0,2) != 'FC' && substr($PROGRAM->kod_Program,0,1) != 'U') && ($jpengajian->KOD_PENGAJIAN =='D2'))
					<li>Calon mestilah <b>memiliki Sijil Pelajaran Malaysia (SPM)</b> dengan mendapat sekurang-kurangnya <b>lulus dalam mata pelajaran Bahasa Melayu DAN Sejarah</b>.</li>

				@else
					<li>Memiliki <b>Sijil Pelajaran Malaysia (SPM)</b> dengan mendapat sekurang-kurangnya :-</li>

				@endif


            @endif

            @if($jpengajian->KOD_PENGAJIAN=='S' && $PROGRAM->jpengajian==$jpengajian->KOD_PENGAJIAN)

                @if(substr($PROGRAM->kod_Program,0,2) =='FC')
                    @foreach ($syaratam_am as $syaratam)
                        @if($syaratam->KODSUBJEK=='W')
                        <li>Warganegara Malaysia yang mempunyai <b>No. Kad Pengenalan / My Kad</b>.</li>
                        @endif
                    @endforeach

                    <li>Pemohon mestilah menduduki <b>Sijil Pelajaran Malaysia (SPM)</b> dan boleh membaca, menulis dan mengira. </li>

                @endif

                @if(substr($PROGRAM->kod_Program,0,2) !='FC')
                    @foreach ($syaratam_am as $syaratam)
                        @if($syaratam->KODSUBJEK=='W')
                        <li>Warganegara Malaysia yang mempunyai <b>No. Kad Pengenalan / My Kad</b>.</li>
                        @endif
                    @endforeach

                    <li>Pemohon mestilah menduduki <b>Sijil Pelajaran Malaysia (SPM)</b> dan boleh membaca, menulis dan mengira dalam Bahasa Melayu ATAU Bahasa Inggeris. </li>

                @endif


            @endif


				@if((substr($PROGRAM->kod_Program,0,2) =='FB' || substr($PROGRAM->kod_Program,0,2) =='FC') && $jpengajian->KOD_PENGAJIAN =='A')
					<ol type="i" style="padding-left: 1.5em;">
						@if($jpengajian->KOD_PENGAJIAN!='S' && $PROGRAM->jpengajian==$jpengajian->KOD_PENGAJIAN)
							@foreach ($syaratam_am as $syaratam)
								@if($syaratam->KODSUBJEK!='F' &&  $syaratam->KODSUBJEK!='W' &&  $syaratam->KODSUBJEK!='T')
									<li style="padding-left: .3em;">
										Gred <b>{{$syaratam->MINGRED}}</b> bagi matapelajaran
										@foreach ($subjek as $spm_subjek)
											@if($spm_subjek->kodsubjekspm==$syaratam->KODSUBJEK)
												<b>{{$spm_subjek->ketsubjekspm}}</b>.
											@endif
										@endforeach
									</li>
								@endif

								@php
								if($syaratam->JUMLAH_MIN_SUBJEK=='1') { $read='SATU'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='2') { $read='DUA'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='3') { $read='TIGA'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='4') { $read='EMPAT'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='5') { $read='LIMA'; }
								@endphp


								@if($syaratam->KODSUBJEK=='F')
									<li style="padding-left: .3em;"><b>{{$read}} ({{$syaratam->JUMLAH_MIN_SUBJEK}}) {{$syaratam->MINGRED}}</b> dalam mana-mana matapelajaran yang belum dikira.</li>
								@endif
							@endforeach
						@endif
					</ol>

				@elseif((substr($PROGRAM->kod_Program,0,2) =='FB' || substr($PROGRAM->kod_Program,0,2) =='FC') && $jpengajian->KOD_PENGAJIAN =='D2')
					<ol type="i" style="padding-left: 1.5em;">
						@if($jpengajian->KOD_PENGAJIAN!='S' && $PROGRAM->jpengajian==$jpengajian->KOD_PENGAJIAN)
							@foreach ($syaratam_am as $syaratam)
								@if($syaratam->KODSUBJEK!='F' &&  $syaratam->KODSUBJEK!='W' &&  $syaratam->KODSUBJEK!='T')
									<li style="padding-left: .3em;">
										Gred <b>{{$syaratam->MINGRED}}</b> bagi matapelajaran
										@foreach ($subjek as $spm_subjek)
											@if($spm_subjek->kodsubjekspm==$syaratam->KODSUBJEK)
												<b>{{$spm_subjek->ketsubjekspm}}</b>.
											@endif
										@endforeach
									</li>
								@endif

								@php
								if($syaratam->JUMLAH_MIN_SUBJEK=='1') { $read='SATU'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='2') { $read='DUA'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='3') { $read='TIGA'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='4') { $read='EMPAT'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='5') { $read='LIMA'; }
								@endphp


								@if($syaratam->KODSUBJEK=='F')
									<li style="padding-left: .3em;"><b>{{$read}} ({{$syaratam->JUMLAH_MIN_SUBJEK}}) {{$syaratam->MINGRED}}</b> dalam mana-mana matapelajaran yang belum dikira.</li>
								@endif
							@endforeach
						@endif
					</ol>

				@elseif((substr($PROGRAM->kod_Program,0,2) != 'FB' && substr($PROGRAM->kod_Program,0,2) != 'FC' && substr($PROGRAM->kod_Program,0,1) != 'U') && ($jpengajian->KOD_PENGAJIAN =='A'))
					<ol type="i" style="padding-left: 1.5em;">
						@if($jpengajian->KOD_PENGAJIAN!='S' && $PROGRAM->jpengajian==$jpengajian->KOD_PENGAJIAN)
							@foreach ($syaratam_am as $syaratam)
								@if($syaratam->KODSUBJEK!='F' &&  $syaratam->KODSUBJEK!='W' &&  $syaratam->KODSUBJEK!='T')
									<li style="padding-left: .3em;">
										Gred <b>{{$syaratam->MINGRED}}</b> bagi matapelajaran
										@foreach ($subjek as $spm_subjek)
											@if($spm_subjek->kodsubjekspm==$syaratam->KODSUBJEK)
												<b>{{$spm_subjek->ketsubjekspm}}</b>.
											@endif
										@endforeach
									</li>
								@endif

								@php
								if($syaratam->JUMLAH_MIN_SUBJEK=='1') { $read='SATU'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='2') { $read='DUA'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='3') { $read='TIGA'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='4') { $read='EMPAT'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='5') { $read='LIMA'; }
								@endphp


								@if($syaratam->KODSUBJEK=='F')
									<li style="padding-left: .3em;"><b>{{$read}} ({{$syaratam->JUMLAH_MIN_SUBJEK}}) {{$syaratam->MINGRED}}</b> dalam mana-mana matapelajaran yang belum dikira.</li>
								@endif
							@endforeach
						@endif
					</ol>
				@elseif((substr($PROGRAM->kod_Program,0,2) != 'FB' && substr($PROGRAM->kod_Program,0,2) != 'FC' && substr($PROGRAM->kod_Program,0,1) != 'U') && ($jpengajian->KOD_PENGAJIAN =='D2'))

				@else
					<ol type="i" style="padding-left: 1.5em;">
						@if($jpengajian->KOD_PENGAJIAN!='S' && $PROGRAM->jpengajian==$jpengajian->KOD_PENGAJIAN)
							@foreach ($syaratam_am as $syaratam)
								@if($syaratam->KODSUBJEK!='F' &&  $syaratam->KODSUBJEK!='W' &&  $syaratam->KODSUBJEK!='T')
									<li style="padding-left: .3em;">
										Gred <b>{{$syaratam->MINGRED}}</b> bagi matapelajaran
										@foreach ($subjek as $spm_subjek)
											@if($spm_subjek->kodsubjekspm==$syaratam->KODSUBJEK)
												<b>{{$spm_subjek->ketsubjekspm}}</b>.
											@endif
										@endforeach
									</li>
								@endif

								@php
								if($syaratam->JUMLAH_MIN_SUBJEK=='1') { $read='SATU'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='2') { $read='DUA'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='3') { $read='TIGA'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='4') { $read='EMPAT'; }
								elseif($syaratam->JUMLAH_MIN_SUBJEK=='5') { $read='LIMA'; }
								@endphp


								@if($syaratam->KODSUBJEK=='F')
									<li style="padding-left: .3em;"><b>{{$read}} ({{$syaratam->JUMLAH_MIN_SUBJEK}}) {{$syaratam->MINGRED}}</b> dalam mana-mana matapelajaran yang belum dikira.</li>
								@endif
							@endforeach
						@endif
					</ol>
				@endif



        </ol>
    </div>
@endforeach
