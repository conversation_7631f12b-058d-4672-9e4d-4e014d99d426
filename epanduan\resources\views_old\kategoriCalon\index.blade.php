@extends('layout.layout')
@section('content')
    <section class="bg-half bg-light d-table w-100">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-12 text-center">
                    <div class="page-next-level">
                        <h2 class="title"> KATEGORI CALON </h2>
                        <div class="page-next">
                            <nav aria-label="breadcrumb" class="d-inline-block">
                                <ul class="breadcrumb bg-white rounded shadow mb-0">
                                    <li class="breadcrumb-item"><a href="{{ url('/') }}"><PERSON><PERSON></a></li>
                                    <li class="breadcrumb-item"><a href="#">Kate<PERSON><PERSON></a></li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="position-relative">
        <div class="shape overflow-hidden text-white">
            <svg viewBox="0 0 2880 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 48H1437.5H2880V0H2160C1442.5 52 720 0 720 0H0V48Z" fill="currentColor"></path>
            </svg>
        </div>
    </div>

    <section class="section">
        <div class="container">

            @if (session()->get('jenprog') == 'spm')
                <div class="container">
                    <div class="row-list">
                        @foreach ($JENIS_KATEGORI->whereIn('kodkatag', ['A', 'B']) as $KATEGORI_CALON_SPM)
                            <div class="col-lg-4 col-md-6 col-12 mb-4 pb-2">
                                <a href="{{ url('ProgramPengajian/kategoriCalon/' . $KATEGORI_CALON_SPM->kodkatag) }}">
                                    <div
                                        class="card work-container work-modern position-relative overflow-hidden shadow rounded border-0">
                                        <div class="card-bidang p-0">
                                            <img src="{{ asset('/assets/images/_backgroundBidang/awam.jpg') }}"
                                                class="img-fluid" alt="">
                                            <div class="overlay-work bg-dark"></div>
                                        </div>
                                        <div class="content">
                                            <a
                                                class=" text-white d-block font-weight-bold">{{ $KATEGORI_CALON_SPM->kodkatag }} : {{ $KATEGORI_CALON_SPM->ketkatag }}</a>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            @if (session()->get('jenprog') == 'stpm')
                <div class="container">
                    <div class="row-list">
                        <div class="col-12 mt-4 pt-2">
                            <div class="section-title">
                                <h2 class="titleHeader" style="text-align: center; color:#1818189c">LEPASAN STPM
                                </h2>
                            </div>
                        </div>
                        @foreach ($JENIS_KATEGORI->whereIn('kodkatag', ['A', 'S']) as $KATEGORI_CALON_STPM)
                            <div class="col-lg-4 col-md-6 col-12 mb-4 pb-2">
                                <a href="{{ url('ProgramPengajian/kategoriCalon/' . $KATEGORI_CALON_STPM->kodkatag) }}">
                                    <div
                                        class="card work-container work-modern position-relative overflow-hidden shadow rounded border-0">
                                        <div class="card-bidang p-0">
                                            <img src="{{ asset('/assets/images/_backgroundBidang/awam.jpg') }}"
                                                class="img-fluid" alt="">
                                            <div class="overlay-work bg-dark"></div>
                                        </div>
                                        <div class="content">
                                            <a
                                                class=" text-white d-block font-weight-bold">{{ $KATEGORI_CALON_STPM->kodkatag }} : {{ $KATEGORI_CALON_STPM->ketkatag }}</a>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
                <div class="container" style="padding-top: 7rem">
                    <div class="row-list">
                        <div class="col-12 mt-4 pt-2">
                            <div class="section-title">
                                <h2 class="titleHeader" style="text-align: center; color:#1818189c">LEPASAN MATRIKULASI / ASASI</h2>
                            </div>
                        </div>
                        @foreach ($JENIS_KATEGORI->whereIn('kodkatag', ['P', 'L', 'U', 'N', 'K', 'J','M','V']) as $KATEGORI_CALON_MATRIK)
                            <div class="col-lg-4 col-md-6 col-12 mb-4 pb-2">
                                <a href="{{ url('ProgramPengajian/kategoriCalon/' . $KATEGORI_CALON_MATRIK->kodkatag) }}">
                                    <div
                                        class="card work-container work-modern position-relative overflow-hidden shadow rounded border-0">
                                        <div class="card-bidang p-0">
                                            <img src="{{ asset('/assets/images/_backgroundBidang/awam.jpg') }}"
                                                class="img-fluid" alt="">
                                            <div class="overlay-work bg-dark"></div>
                                        </div>
                                        <div class="content">
                                            <a class=" text-white d-block font-weight-bold">{{ $KATEGORI_CALON_MATRIK->kodkatag }} : {{ $KATEGORI_CALON_MATRIK->ketkatag }}</a>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
                <div class="container" style="padding-top: 7rem">
                    <div class="row-list">
                        <div class="col-12 mt-4 pt-2">
                            <div class="section-title">
                                <h2 class="titleHeader" style="text-align: center; color:#1818189c">LEPASAN STAM
                                </h2>
                            </div>
                        </div>
                        @foreach ($JENIS_KATEGORI->whereIn('kodkatag', ['T']) as $KATEGORI_CALON_STAM)
                            <div class="col-lg-4 col-md-6 col-12 mb-4 pb-2">
                                <a href="{{ url('ProgramPengajian/kategoriCalon/' . $KATEGORI_CALON_STAM->kodkatag) }}">
                                    <div
                                        class="card work-container work-modern position-relative overflow-hidden shadow rounded border-0">
                                        <div class="card-bidang p-0">
                                            <img src="{{ asset('/assets/images/_backgroundBidang/awam.jpg') }}"
                                                class="img-fluid" alt="">
                                            <div class="overlay-work bg-dark"></div>
                                        </div>
                                        <div class="content">
                                            <a
                                                class=" text-white d-block font-weight-bold">{{ $KATEGORI_CALON_STAM->kodkatag }} : {{ $KATEGORI_CALON_STAM->ketkatag }}</a>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>

                <div class="container" style="padding-top: 7rem">
                    <div class="row-list">
                        <div class="col-12 mt-4 pt-2">
                            <div class="section-title">
                                <h2 class="titleHeader" style="text-align: center; color:#1818189c">KELAYAKAN SETARAF
                                </h2>
                            </div>
                        </div>
                        @foreach ($JENIS_KATEGORI->whereNotIn('kodkatag', ['P', 'L', 'U', 'N', 'K', 'J','M','V', 'A', 'S', 'T']) as $KATEGORI_CALON_DIPLOMA)
                            <div class="col-lg-4 col-md-6 col-12 mb-4 pb-2">
                                <a href="{{ url('ProgramPengajian/kategoriCalon/' . $KATEGORI_CALON_DIPLOMA->kodkatag) }}">
                                    <div
                                        class="card work-container work-modern position-relative overflow-hidden shadow rounded border-0">
                                        <div class="card-bidang p-0">
                                            <img src="{{ asset('/assets/images/_backgroundBidang/awam.jpg') }}"
                                                class="img-fluid" alt="">
                                            <div class="overlay-work bg-dark"></div>
                                        </div>
                                        <div class="content">
                                            <a
                                                class=" text-white d-block font-weight-bold">{{ $KATEGORI_CALON_DIPLOMA->kodkatag }} : {{ $KATEGORI_CALON_DIPLOMA->ketkatag }}</a>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </section>
@endsection
